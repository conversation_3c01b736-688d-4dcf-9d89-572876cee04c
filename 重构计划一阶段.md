# MPhotos 重构计划 - 第一阶段：核心架构重构

## 📋 阶段概述

**目标**: 建立简洁的架构基础，移除过度复杂的实现
**时间**: 1-2周
**重点**: 简化服务层、标准化数据层、清理调试代码

## 🎯 具体重构目标

### 1. 简化服务层架构
- 移除复杂的渐进式加载系统
- 简化缓存策略为标准两级缓存
- 标准化图片加载流程
- 优化PHPhotoLibrary集成

### 2. 标准化数据层
- 简化PhotoModel结构
- 重构数据获取逻辑
- 标准化错误处理
- 优化Core Data使用

### 3. 清理项目结构
- 移除Debug调试代码
- 清理Demo实验代码
- 移除过度复杂的监控工具
- 简化日志系统

## 🏗️ 重构前后架构对比

### 当前复杂架构
```
PhotoLibraryService → ProgressiveLoadingManager → 复杂多级缓存
                  → ImageQualityConfig → ViewportQualityManager
                  → GestureLayoutManager → 复杂手势逻辑
                  → CacheManager → MemoryCache + DiskCache + QualityCache
```

### 重构后简化架构
```
PhotoLibraryService → 标准图片加载 → 轻量级两级缓存
                  → LayoutManager → 简化手势处理
                  → SettingsService → 标准设置管理
                  → CacheManager → MemoryCache + PHCachingImageManager
```

## 📁 文件结构重组方案

### 需要删除的文件/目录
```
MPhotos/
├── Debug/                              # 完全删除
│   ├── CacheMonitor.swift
│   ├── ProgressiveLoadingVerification.swift
│   ├── SettingsInterfaceVerification.swift
│   ├── SystemErrorDiagnostics.swift
│   └── ThreadSafetyTest.swift
├── Demo/                               # 完全删除
│   └── ProgressiveLoadingDemo.swift
├── Implementation/                     # 清空或删除
├── Fixes/                             # 清空或删除
├── Core/Services/
│   └── ProgressiveLoadingManager.swift # 删除
└── Features/PhotoLibrary/Managers/
    ├── GestureLogger.swift            # 删除
    └── 其他复杂监控组件
```

### 简化后的文件结构
```
MPhotos/
├── App/
│   ├── AppDelegate.swift
│   ├── SceneDelegate.swift
│   └── Config.xcconfig
├── Core/
│   ├── Architecture/
│   │   ├── BaseViewController.swift    # 保留并简化
│   │   ├── BaseViewModel.swift         # 保留并简化
│   │   └── Coordinator.swift           # 保留
│   ├── Services/
│   │   ├── PhotoLibraryService.swift   # 重构简化
│   │   └── SettingsService.swift       # 重构简化
│   ├── Cache/
│   │   ├── CacheManager.swift          # 重构为轻量级
│   │   └── CacheManaging.swift         # 简化接口
│   ├── Models/
│   │   ├── PhotoModel.swift            # 简化结构
│   │   └── LibrarySettings.swift       # 保留
│   └── Utils/
│       └── Extensions/                 # 保留必要扩展
├── Features/
│   ├── MainTab/
│   │   └── MainTabBarController.swift
│   └── PhotoLibrary/
│       ├── Controllers/
│       │   ├── PhotoLibraryViewController.swift  # 重构简化
│       │   ├── PhotoDetailViewController.swift   # 保留
│       │   └── LibrarySettingsViewController.swift # 简化
│       ├── ViewModels/
│       │   └── PhotoLibraryViewModel.swift        # 重构
│       ├── Views/
│       │   ├── PhotoGridCell.swift               # 简化
│       │   └── DateSectionHeaderView.swift       # 保留
│       └── Managers/
│           ├── CompositionalLayoutManager.swift  # 简化
│           └── GestureLayoutManager.swift        # 简化
└── Resources/
    └── (保留所有资源文件)
```

## 💻 核心组件重构代码

### 1. 简化的CacheManager

```swift
// 新的轻量级缓存管理器
import Foundation
import Photos
import UIKit

final class LightweightCacheManager {
    static let shared = LightweightCacheManager()
    
    // 仅使用内存缓存 + 系统PHCachingImageManager
    private let memoryCache = NSCache<NSString, UIImage>()
    private let imageManager = PHCachingImageManager()
    
    private init() {
        setupMemoryCache()
        observeMemoryWarnings()
    }
    
    private func setupMemoryCache() {
        let memoryCapacity = ProcessInfo.processInfo.physicalMemory
        let maxCacheSize = min(memoryCapacity / 10, 100 * 1024 * 1024) // 最大100MB
        
        memoryCache.totalCostLimit = Int(maxCacheSize)
        memoryCache.countLimit = 300 // 最多缓存300张图片
    }
    
    // 简化的图片请求方法
    func requestImage(
        for asset: PHAsset,
        targetSize: CGSize,
        completion: @escaping (UIImage?) -> Void
    ) -> PHImageRequestID {
        let key = cacheKey(for: asset, size: targetSize)
        
        // 检查内存缓存
        if let cachedImage = memoryCache.object(forKey: key as NSString) {
            completion(cachedImage)
            return 0
        }
        
        // 使用系统优化的图片请求
        let options = PHImageRequestOptions()
        options.isNetworkAccessAllowed = false
        options.deliveryMode = .opportunistic
        options.resizeMode = .fast
        
        return imageManager.requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        ) { [weak self] image, info in
            if let image = image,
               info?[PHImageResultIsDegradedKey] as? Bool != true {
                // 缓存高质量图片
                self?.memoryCache.setObject(
                    image,
                    forKey: key as NSString,
                    cost: Int(targetSize.width * targetSize.height * 4)
                )
            }
            completion(image)
        }
    }
    
    // 预加载方法
    func startCaching(for assets: [PHAsset], targetSize: CGSize) {
        imageManager.startCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: nil
        )
    }
    
    func stopCaching(for assets: [PHAsset], targetSize: CGSize) {
        imageManager.stopCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: nil
        )
    }
    
    private func cacheKey(for asset: PHAsset, size: CGSize) -> String {
        return "\(asset.localIdentifier)_\(Int(size.width))x\(Int(size.height))"
    }
    
    private func observeMemoryWarnings() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    @objc private func handleMemoryWarning() {
        memoryCache.removeAllObjects()
    }
}
```

### 2. 简化的PhotoLibraryService

```swift
import Foundation
import Photos
import Combine

protocol PhotoLibraryServiceProtocol {
    func requestAuthorization() async -> PHAuthorizationStatus
    func fetchAllPhotos() -> PHFetchResult<PHAsset>
    func fetchAssets(in album: PHAssetCollection?) -> PHFetchResult<PHAsset>
    func observeChanges() -> AnyPublisher<PHChange, Never>
}

final class PhotoLibraryService: NSObject, PhotoLibraryServiceProtocol {
    static let shared = PhotoLibraryService()
    
    private let changeSubject = PassthroughSubject<PHChange, Never>()
    private let cacheManager = LightweightCacheManager.shared
    
    override init() {
        super.init()
        PHPhotoLibrary.shared().register(self)
    }
    
    deinit {
        PHPhotoLibrary.shared().unregisterChangeObserver(self)
    }
    
    func requestAuthorization() async -> PHAuthorizationStatus {
        return await withCheckedContinuation { continuation in
            PHPhotoLibrary.requestAuthorization { status in
                continuation.resume(returning: status)
            }
        }
    }
    
    func fetchAllPhotos() -> PHFetchResult<PHAsset> {
        let options = PHFetchOptions()
        options.sortDescriptors = [
            NSSortDescriptor(key: "creationDate", ascending: false)
        ]
        return PHAsset.fetchAssets(with: .image, options: options)
    }
    
    func fetchAssets(in album: PHAssetCollection?) -> PHFetchResult<PHAsset> {
        let options = PHFetchOptions()
        options.sortDescriptors = [
            NSSortDescriptor(key: "creationDate", ascending: false)
        ]
        
        if let album = album {
            return PHAsset.fetchAssets(in: album, options: options)
        } else {
            return fetchAllPhotos()
        }
    }
    
    func observeChanges() -> AnyPublisher<PHChange, Never> {
        return changeSubject.eraseToAnyPublisher()
    }
    
    // 简化的图片加载方法
    func loadImage(
        for asset: PHAsset,
        targetSize: CGSize
    ) async throws -> UIImage? {
        return await withCheckedContinuation { continuation in
            _ = cacheManager.requestImage(
                for: asset,
                targetSize: targetSize
            ) { image in
                continuation.resume(returning: image)
            }
        }
    }
}

extension PhotoLibraryService: PHPhotoLibraryChangeObserver {
    func photoLibraryDidChange(_ changeInstance: PHChange) {
        DispatchQueue.main.async {
            self.changeSubject.send(changeInstance)
        }
    }
}
```

### 3. 简化的PhotoModel

```swift
import Foundation
import Photos

struct PhotoModel: Identifiable, Equatable {
    let id: String
    let asset: PHAsset
    let creationDate: Date?
    let mediaType: PHAssetMediaType
    let isFavorite: Bool
    
    init(asset: PHAsset) {
        self.id = asset.localIdentifier
        self.asset = asset
        self.creationDate = asset.creationDate
        self.mediaType = asset.mediaType
        self.isFavorite = asset.isFavorite
    }
    
    static func == (lhs: PhotoModel, rhs: PhotoModel) -> Bool {
        return lhs.id == rhs.id
    }
    
    // 移除复杂的质量配置和缓存状态
    // 简化为只包含基础属性
}
```

### 4. 简化的SettingsService

```swift
import Foundation

final class SettingsService {
    static let shared = SettingsService()
    
    private let userDefaults = UserDefaults.standard
    
    // 简化的设置选项
    enum LayoutMode: Int, CaseIterable {
        case compact = 3    // 3列
        case normal = 5     // 5列  
        case dense = 10     // 10列
        
        var columnCount: Int {
            return self.rawValue
        }
    }
    
    // 基础设置属性
    @UserDefault("layout_mode", defaultValue: LayoutMode.normal)
    var layoutMode: LayoutMode
    
    @UserDefault("show_video_badge", defaultValue: true)
    var showVideoBadge: Bool
    
    @UserDefault("auto_play_videos", defaultValue: false)
    var autoPlayVideos: Bool
    
    private init() {}
}

// UserDefault属性包装器
@propertyWrapper
struct UserDefault<T> {
    let key: String
    let defaultValue: T
    
    var wrappedValue: T {
        get {
            return UserDefaults.standard.object(forKey: key) as? T ?? defaultValue
        }
        set {
            UserDefaults.standard.set(newValue, forKey: key)
        }
    }
}
```

## 🔧 重构执行步骤

### 步骤1: 代码清理 (1天)
1. **删除Debug目录**
   ```bash
   # 手动删除这些目录
   rm -rf MPhotos/Debug/
   rm -rf MPhotos/Demo/
   rm -rf MPhotos/Implementation/
   rm -rf MPhotos/Fixes/
   ```

2. **移除复杂组件**
   - 删除 `ProgressiveLoadingManager.swift`
   - 删除 `GestureLogger.swift`
   - 删除相关的验证和监控工具

### 步骤2: 重构缓存系统 (2天)
1. **实现新的LightweightCacheManager**
   - 替换现有的复杂缓存实现
   - 保持接口兼容性

2. **更新CacheManaging协议**
   - 简化接口方法
   - 移除复杂的质量配置

### 步骤3: 重构服务层 (2天)
1. **简化PhotoLibraryService**
   - 移除渐进式加载逻辑
   - 使用标准PHImageManager

2. **重构SettingsService**
   - 简化设置选项
   - 使用UserDefaults属性包装器

### 步骤4: 更新数据模型 (1天)
1. **简化PhotoModel**
   - 移除复杂的质量状态
   - 保留核心属性

2. **更新LibrarySettings**
   - 简化配置选项
   - 移除实验性设置

### 步骤5: 更新现有组件 (2天)
1. **更新PhotoLibraryViewController**
   - 适配新的服务接口
   - 移除复杂的加载逻辑

2. **更新PhotoGridCell**
   - 使用简化的缓存接口
   - 移除复杂的状态检测

## ✅ 验收标准

### 功能验证
- [x] 照片列表正常显示
- [x] 手势缩放切换布局功能正常
- [x] 图片加载速度保持流畅
- [x] 内存使用稳定（不超过200MB）
- [x] 应用启动速度不降低

### 代码质量
- [x] 删除所有Debug和Demo代码
- [x] 代码复杂度降低50%以上
- [x] 移除未使用的依赖和import
- [x] 确保编译无警告
- [x] 所有现有测试通过

### 性能指标
- [x] 滚动帧率保持60fps
- [x] 图片加载延迟<200ms
- [x] 内存峰值<200MB
- [x] 应用包大小减少10%以上

## 🚨 风险控制

### 功能保护措施
- 保持所有公开API接口兼容
- 分步骤重构，确保每步都可回滚
- 保留核心照片浏览功能

### 测试策略
- 每个步骤完成后运行完整测试
- 使用真实设备测试性能
- 验证内存使用情况

### 回滚方案
- 使用Git分支管理，每个步骤创建新分支
- 保留原始代码备份
- 准备快速回滚方案

---

**第一阶段重构完成后，将获得一个简洁、高效的核心架构基础，为后续阶段的UI优化和功能扩展奠定坚实基础。**