# MPhotos 重构计划 - 第三阶段：功能完善

## 📋 阶段概述

**目标**: 实现完整的照片应用功能
**时间**: 2-3周
**前置条件**: 第一、二阶段重构完成
**重点**: 相簿管理、搜索功能、批量操作、用户体验优化

## 🎯 具体功能目标

### 1. 相簿管理功能
- 实现相簿列表展示
- 支持创建/删除自定义相簿
- 照片移动和复制功能
- 相簿封面和排序

### 2. 搜索功能
- 实现照片内容搜索
- 支持日期、类型筛选
- 智能分组显示
- 搜索历史记录

### 3. 批量操作
- 完善多选功能
- 批量删除/移动
- 批量分享
- 操作撤销机制

### 4. 用户体验优化
- 改进转场动画
- 完善反馈机制
- 添加空状态处理
- 优化加载状态

## 🏗️ 功能架构设计

### 新增功能模块结构
```
MPhotos/
├── Features/
│   ├── Albums/                         # 新增相簿模块
│   │   ├── Controllers/
│   │   │   ├── AlbumsViewController.swift
│   │   │   └── AlbumDetailViewController.swift
│   │   ├── ViewModels/
│   │   │   ├── AlbumsViewModel.swift
│   │   │   └── AlbumDetailViewModel.swift
│   │   ├── Views/
│   │   │   ├── AlbumGridCell.swift
│   │   │   └── CreateAlbumView.swift
│   │   └── Managers/
│   │       └── AlbumManager.swift
│   ├── Search/                         # 新增搜索模块
│   │   ├── Controllers/
│   │   │   └── SearchViewController.swift
│   │   ├── ViewModels/
│   │   │   └── SearchViewModel.swift
│   │   ├── Views/
│   │   │   ├── SearchBar.swift
│   │   │   ├── FilterView.swift
│   │   │   └── SearchResultCell.swift
│   │   └── Managers/
│   │       └── SearchManager.swift
│   ├── Selection/                      # 新增批量操作模块
│   │   ├── Controllers/
│   │   │   └── SelectionViewController.swift
│   │   ├── ViewModels/
│   │   │   └── SelectionViewModel.swift
│   │   ├── Views/
│   │   │   └── SelectionToolbar.swift
│   │   └── Managers/
│   │       └── BatchOperationManager.swift
│   └── PhotoLibrary/                   # 扩展现有模块
│       ├── Controllers/
│       │   ├── PhotoLibraryViewController.swift  # 扩展选择模式
│       │   └── PhotoDetailViewController.swift   # 扩展分享功能
│       └── Views/
│           └── PhotoGridCell.swift               # 扩展选择状态
```

## 💻 核心功能模块实现

### 1. 相簿管理模块

#### AlbumManager - 相簿数据管理
```swift
import Foundation
import Photos
import Combine

protocol AlbumManaging {
    func fetchAlbums() -> AnyPublisher<[AlbumModel], Error>
    func createAlbum(title: String) async throws -> AlbumModel
    func deleteAlbum(_ album: AlbumModel) async throws
    func addAssets(_ assets: [PHAsset], to album: AlbumModel) async throws
    func removeAssets(_ assets: [PHAsset], from album: AlbumModel) async throws
    func updateAlbumTitle(_ album: AlbumModel, newTitle: String) async throws
}

final class AlbumManager: NSObject, AlbumManaging {
    static let shared = AlbumManager()
    
    private let changeSubject = PassthroughSubject<PHChange, Never>()
    
    override init() {
        super.init()
        PHPhotoLibrary.shared().register(self)
    }
    
    deinit {
        PHPhotoLibrary.shared().unregisterChangeObserver(self)
    }
    
    func fetchAlbums() -> AnyPublisher<[AlbumModel], Error> {
        return Future { promise in
            DispatchQueue.global(qos: .userInitiated).async {
                do {
                    var albums: [AlbumModel] = []
                    
                    // 获取系统相簿
                    let systemAlbums = PHAssetCollection.fetchAssetCollections(
                        with: .smartAlbum,
                        subtype: .any,
                        options: nil
                    )
                    
                    systemAlbums.enumerateObjects { collection, _, _ in
                        if let album = self.createAlbumModel(from: collection) {
                            albums.append(album)
                        }
                    }
                    
                    // 获取用户相簿
                    let userAlbums = PHAssetCollection.fetchAssetCollections(
                        with: .album,
                        subtype: .any,
                        options: nil
                    )
                    
                    userAlbums.enumerateObjects { collection, _, _ in
                        if let album = self.createAlbumModel(from: collection) {
                            albums.append(album)
                        }
                    }
                    
                    promise(.success(albums))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func createAlbum(title: String) async throws -> AlbumModel {
        return try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges({
                PHAssetCollectionChangeRequest.creationRequestForAssetCollection(withTitle: title)
            }) { success, error in
                if success {
                    // 重新获取刚创建的相簿
                    let options = PHFetchOptions()
                    options.predicate = NSPredicate(format: "localizedTitle == %@", title)
                    
                    let collections = PHAssetCollection.fetchAssetCollections(
                        with: .album,
                        subtype: .any,
                        options: options
                    )
                    
                    if let collection = collections.firstObject,
                       let album = self.createAlbumModel(from: collection) {
                        continuation.resume(returning: album)
                    } else {
                        continuation.resume(throwing: PhotosAppError.albumCreationFailed)
                    }
                } else {
                    continuation.resume(throwing: error ?? PhotosAppError.albumCreationFailed)
                }
            }
        }
    }
    
    func deleteAlbum(_ album: AlbumModel) async throws {
        guard album.canDelete else {
            throw PhotosAppError.albumCannotBeDeleted
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges({
                PHAssetCollectionChangeRequest.deleteAssetCollections([album.collection] as NSFastEnumeration)
            }) { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosAppError.albumDeletionFailed)
                }
            }
        }
    }
    
    func addAssets(_ assets: [PHAsset], to album: AlbumModel) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges({
                if let changeRequest = PHAssetCollectionChangeRequest(for: album.collection) {
                    changeRequest.addAssets(assets as NSFastEnumeration)
                }
            }) { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosAppError.addToAlbumFailed)
                }
            }
        }
    }
    
    func removeAssets(_ assets: [PHAsset], from album: AlbumModel) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges({
                if let changeRequest = PHAssetCollectionChangeRequest(for: album.collection) {
                    changeRequest.removeAssets(assets as NSFastEnumeration)
                }
            }) { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosAppError.removeFromAlbumFailed)
                }
            }
        }
    }
    
    func updateAlbumTitle(_ album: AlbumModel, newTitle: String) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges({
                if let changeRequest = PHAssetCollectionChangeRequest(for: album.collection) {
                    changeRequest.title = newTitle
                }
            }) { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosAppError.albumRenameFailed)
                }
            }
        }
    }
    
    private func createAlbumModel(from collection: PHAssetCollection) -> AlbumModel? {
        let assetCount = PHAsset.fetchAssets(in: collection, options: nil).count
        
        // 过滤掉空相簿和某些系统相簿
        guard assetCount > 0,
              collection.localizedTitle != nil,
              !shouldSkipSystemAlbum(collection) else {
            return nil
        }
        
        return AlbumModel(collection: collection)
    }
    
    private func shouldSkipSystemAlbum(_ collection: PHAssetCollection) -> Bool {
        let skipSubtypes: [PHAssetCollectionSubtype] = [
            .smartAlbumRecentlyAdded,
            .smartAlbumUserLibrary,
            .smartAlbumAllHidden
        ]
        return skipSubtypes.contains(collection.assetCollectionSubtype)
    }
}

extension AlbumManager: PHPhotoLibraryChangeObserver {
    func photoLibraryDidChange(_ changeInstance: PHChange) {
        DispatchQueue.main.async {
            self.changeSubject.send(changeInstance)
        }
    }
}
```

#### AlbumModel - 相簿数据模型
```swift
import Foundation
import Photos

struct AlbumModel: Identifiable, Equatable {
    let id: String
    let collection: PHAssetCollection
    let title: String
    let assetCount: Int
    let type: AlbumType
    let canDelete: Bool
    let canRename: Bool
    
    enum AlbumType {
        case system
        case user
        case shared
    }
    
    init(collection: PHAssetCollection) {
        self.id = collection.localIdentifier
        self.collection = collection
        self.title = collection.localizedTitle ?? "未命名相簿"
        self.assetCount = PHAsset.fetchAssets(in: collection, options: nil).count
        
        switch collection.assetCollectionType {
        case .album:
            self.type = .user
            self.canDelete = true
            self.canRename = true
        case .smartAlbum:
            self.type = .system
            self.canDelete = false
            self.canRename = false
        default:
            self.type = .shared
            self.canDelete = false
            self.canRename = false
        }
    }
    
    static func == (lhs: AlbumModel, rhs: AlbumModel) -> Bool {
        return lhs.id == rhs.id
    }
    
    func fetchAssets() -> PHFetchResult<PHAsset> {
        let options = PHFetchOptions()
        options.sortDescriptors = [
            NSSortDescriptor(key: "creationDate", ascending: false)
        ]
        return PHAsset.fetchAssets(in: collection, options: options)
    }
    
    func fetchCoverAsset() -> PHAsset? {
        let options = PHFetchOptions()
        options.fetchLimit = 1
        options.sortDescriptors = [
            NSSortDescriptor(key: "creationDate", ascending: false)
        ]
        return PHAsset.fetchAssets(in: collection, options: options).firstObject
    }
}
```

#### AlbumsViewController - 相簿列表界面
```swift
import UIKit
import Combine

final class AlbumsViewController: UIViewController {
    
    // MARK: - UI Components
    private lazy var collectionView: UICollectionView = {
        let layout = createLayout()
        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .systemBackground
        cv.delegate = self
        cv.dataSource = self
        cv.register(AlbumGridCell.self, forCellWithReuseIdentifier: AlbumGridCell.identifier)
        return cv
    }()
    
    private lazy var addButton: UIBarButtonItem = {
        return UIBarButtonItem(
            barButtonSystemItem: .add,
            target: self,
            action: #selector(createNewAlbum)
        )
    }()
    
    // MARK: - Properties
    private let viewModel: AlbumsViewModel
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(viewModel: AlbumsViewModel = AlbumsViewModel()) {
        self.viewModel = viewModel
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupBindings()
        
        Task {
            await viewModel.loadAlbums()
        }
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "相簿"
        
        navigationItem.rightBarButtonItem = addButton
        
        view.addSubview(collectionView)
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func setupBindings() {
        viewModel.$albums
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.collectionView.reloadData()
            }
            .store(in: &cancellables)
        
        viewModel.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoading in
                // 显示/隐藏加载指示器
                if isLoading {
                    self?.showLoadingIndicator()
                } else {
                    self?.hideLoadingIndicator()
                }
            }
            .store(in: &cancellables)
        
        viewModel.$error
            .compactMap { $0 }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                self?.showErrorAlert(error)
            }
            .store(in: &cancellables)
    }
    
    private func createLayout() -> UICollectionViewLayout {
        let itemSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(0.5),
            heightDimension: .estimated(200)
        )
        let item = NSCollectionLayoutItem(layoutSize: itemSize)
        
        let groupSize = NSCollectionLayoutSize(
            widthDimension: .fractionalWidth(1.0),
            heightDimension: .estimated(200)
        )
        let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize, subitems: [item])
        group.interItemSpacing = .fixed(16)
        
        let section = NSCollectionLayoutSection(group: group)
        section.interGroupSpacing = 16
        section.contentInsets = NSDirectionalEdgeInsets(top: 16, leading: 16, bottom: 16, trailing: 16)
        
        return UICollectionViewCompositionalLayout(section: section)
    }
    
    // MARK: - Actions
    @objc private func createNewAlbum() {
        showCreateAlbumAlert()
    }
    
    private func showCreateAlbumAlert() {
        let alert = UIAlertController(title: "新建相簿", message: "请输入相簿名称", preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.placeholder = "相簿名称"
            textField.autocapitalizationType = .words
        }
        
        let createAction = UIAlertAction(title: "创建", style: .default) { [weak self] _ in
            guard let title = alert.textFields?.first?.text, !title.isEmpty else { return }
            Task {
                await self?.viewModel.createAlbum(title: title)
            }
        }
        
        alert.addAction(createAction)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alert, animated: true)
    }
    
    private func showErrorAlert(_ error: Error) {
        let alert = UIAlertController(
            title: "错误",
            message: error.localizedDescription,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    private func showLoadingIndicator() {
        // 实现加载指示器
    }
    
    private func hideLoadingIndicator() {
        // 隐藏加载指示器
    }
}

// MARK: - UICollectionViewDataSource
extension AlbumsViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return viewModel.albums.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: AlbumGridCell.identifier, for: indexPath) as! AlbumGridCell
        let album = viewModel.albums[indexPath.item]
        cell.configure(with: album)
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension AlbumsViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let album = viewModel.albums[indexPath.item]
        let detailVC = AlbumDetailViewController(album: album)
        navigationController?.pushViewController(detailVC, animated: true)
    }
    
    func collectionView(_ collectionView: UICollectionView, contextMenuConfigurationForItemAt indexPath: IndexPath, point: CGPoint) -> UIContextMenuConfiguration? {
        let album = viewModel.albums[indexPath.item]
        
        return UIContextMenuConfiguration(identifier: nil, previewProvider: nil) { _ in
            var actions: [UIAction] = []
            
            if album.canRename {
                let renameAction = UIAction(title: "重命名", image: UIImage(systemName: "pencil")) { _ in
                    self.showRenameAlbumAlert(for: album)
                }
                actions.append(renameAction)
            }
            
            if album.canDelete {
                let deleteAction = UIAction(title: "删除", image: UIImage(systemName: "trash"), attributes: .destructive) { _ in
                    self.showDeleteAlbumAlert(for: album)
                }
                actions.append(deleteAction)
            }
            
            return UIMenu(title: "", children: actions)
        }
    }
    
    private func showRenameAlbumAlert(for album: AlbumModel) {
        let alert = UIAlertController(title: "重命名相簿", message: "请输入新的相簿名称", preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.text = album.title
            textField.selectAll(nil)
        }
        
        let renameAction = UIAlertAction(title: "重命名", style: .default) { [weak self] _ in
            guard let newTitle = alert.textFields?.first?.text, !newTitle.isEmpty else { return }
            Task {
                await self?.viewModel.renameAlbum(album, newTitle: newTitle)
            }
        }
        
        alert.addAction(renameAction)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alert, animated: true)
    }
    
    private func showDeleteAlbumAlert(for album: AlbumModel) {
        let alert = UIAlertController(
            title: "删除相簿",
            message: "确定要删除"\(album.title)"吗？此操作不可撤销。",
            preferredStyle: .alert
        )
        
        let deleteAction = UIAlertAction(title: "删除", style: .destructive) { [weak self] _ in
            Task {
                await self?.viewModel.deleteAlbum(album)
            }
        }
        
        alert.addAction(deleteAction)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        present(alert, animated: true)
    }
}
```

### 2. 搜索功能模块

#### SearchManager - 搜索数据管理
```swift
import Foundation
import Photos
import Combine

protocol SearchManaging {
    func search(query: String, filters: SearchFilters) -> AnyPublisher<[PhotoModel], Error>
    func getSearchSuggestions() -> [String]
    func saveSearchHistory(query: String)
    func clearSearchHistory()
}

struct SearchFilters {
    let mediaType: PHAssetMediaType?
    let dateRange: DateInterval?
    let isFavorite: Bool?
    
    static let empty = SearchFilters(mediaType: nil, dateRange: nil, isFavorite: nil)
}

final class SearchManager: SearchManaging {
    static let shared = SearchManager()
    
    private let historyKey = "search_history"
    private let maxHistoryCount = 20
    
    private init() {}
    
    func search(query: String, filters: SearchFilters) -> AnyPublisher<[PhotoModel], Error> {
        return Future { promise in
            DispatchQueue.global(qos: .userInitiated).async {
                do {
                    let options = PHFetchOptions()
                    var predicates: [NSPredicate] = []
                    
                    // 媒体类型过滤
                    if let mediaType = filters.mediaType {
                        predicates.append(NSPredicate(format: "mediaType == %d", mediaType.rawValue))
                    }
                    
                    // 收藏过滤
                    if let isFavorite = filters.isFavorite {
                        predicates.append(NSPredicate(format: "isFavorite == %@", NSNumber(value: isFavorite)))
                    }
                    
                    // 日期范围过滤
                    if let dateRange = filters.dateRange {
                        predicates.append(NSPredicate(
                            format: "creationDate >= %@ AND creationDate <= %@",
                            dateRange.start as NSDate,
                            dateRange.end as NSDate
                        ))
                    }
                    
                    // 文本搜索（这里简化为基于元数据的搜索）
                    if !query.isEmpty {
                        // 可以扩展为使用Vision框架进行图片内容搜索
                        // 目前基于文件名和关键词搜索
                        let textPredicate = NSPredicate(format: "filename CONTAINS[cd] %@", query)
                        predicates.append(textPredicate)
                    }
                    
                    if !predicates.isEmpty {
                        options.predicate = NSCompoundPredicate(andPredicateWithSubpredicates: predicates)
                    }
                    
                    options.sortDescriptors = [
                        NSSortDescriptor(key: "creationDate", ascending: false)
                    ]
                    
                    let fetchResult = PHAsset.fetchAssets(with: options)
                    var photos: [PhotoModel] = []
                    
                    fetchResult.enumerateObjects { asset, _, _ in
                        photos.append(PhotoModel(asset: asset))
                    }
                    
                    promise(.success(photos))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func getSearchSuggestions() -> [String] {
        let history = UserDefaults.standard.stringArray(forKey: historyKey) ?? []
        let commonSuggestions = ["最近拍摄", "收藏", "视频", "照片", "屏幕截图"]
        
        return Array(Set(history + commonSuggestions)).sorted()
    }
    
    func saveSearchHistory(query: String) {
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        var history = UserDefaults.standard.stringArray(forKey: historyKey) ?? []
        
        // 移除重复项
        if let index = history.firstIndex(of: query) {
            history.remove(at: index)
        }
        
        // 添加到开头
        history.insert(query, at: 0)
        
        // 限制历史记录数量
        if history.count > maxHistoryCount {
            history = Array(history.prefix(maxHistoryCount))
        }
        
        UserDefaults.standard.set(history, forKey: historyKey)
    }
    
    func clearSearchHistory() {
        UserDefaults.standard.removeObject(forKey: historyKey)
    }
}
```

### 3. 批量操作模块

#### BatchOperationManager - 批量操作管理
```swift
import Foundation
import Photos
import Combine

enum BatchOperation {
    case delete
    case addToAlbum(AlbumModel)
    case removeFromAlbum(AlbumModel)
    case favorite(Bool)
    case share
}

protocol BatchOperationManaging {
    func performOperation(_ operation: BatchOperation, on assets: [PHAsset]) async throws
    func canPerformOperation(_ operation: BatchOperation, on assets: [PHAsset]) -> Bool
}

final class BatchOperationManager: BatchOperationManaging {
    static let shared = BatchOperationManager()
    
    private init() {}
    
    func performOperation(_ operation: BatchOperation, on assets: [PHAsset]) async throws {
        guard !assets.isEmpty else { return }
        
        return try await withCheckedThrowingContinuation { continuation in
            PHPhotoLibrary.shared().performChanges({
                switch operation {
                case .delete:
                    PHAssetChangeRequest.deleteAssets(assets as NSFastEnumeration)
                    
                case .addToAlbum(let album):
                    if let changeRequest = PHAssetCollectionChangeRequest(for: album.collection) {
                        changeRequest.addAssets(assets as NSFastEnumeration)
                    }
                    
                case .removeFromAlbum(let album):
                    if let changeRequest = PHAssetCollectionChangeRequest(for: album.collection) {
                        changeRequest.removeAssets(assets as NSFastEnumeration)
                    }
                    
                case .favorite(let isFavorite):
                    for asset in assets {
                        if let changeRequest = PHAssetChangeRequest(for: asset) {
                            changeRequest.isFavorite = isFavorite
                        }
                    }
                    
                case .share:
                    // 分享操作不需要在这里处理，由UI层处理
                    break
                }
            }) { success, error in
                if success {
                    continuation.resume()
                } else {
                    continuation.resume(throwing: error ?? PhotosAppError.batchOperationFailed)
                }
            }
        }
    }
    
    func canPerformOperation(_ operation: BatchOperation, on assets: [PHAsset]) -> Bool {
        guard !assets.isEmpty else { return false }
        
        switch operation {
        case .delete:
            return assets.allSatisfy { $0.canPerform(.delete) }
            
        case .addToAlbum, .removeFromAlbum:
            return true // 通常都可以执行
            
        case .favorite:
            return assets.allSatisfy { $0.canPerform(.properties) }
            
        case .share:
            return true // 分享通常都可以执行
        }
    }
}
```

## 🔧 重构执行步骤

### 步骤1: 实现相簿管理功能 (5天)
1. **创建AlbumManager和AlbumModel** (2天)
   - 实现相簿数据获取
   - 支持创建/删除/重命名操作

2. **开发AlbumsViewController** (2天)
   - 相簿列表展示
   - 长按菜单操作

3. **实现AlbumDetailViewController** (1天)
   - 相簿内照片展示
   - 与主照片浏览器集成

### 步骤2: 开发搜索功能 (4天)
1. **实现SearchManager** (2天)
   - 基础文本搜索
   - 过滤器功能

2. **创建SearchViewController** (2天)
   - 搜索界面
   - 结果展示

### 步骤3: 完善批量操作 (4天)
1. **实现BatchOperationManager** (1天)
   - 批量删除/移动/收藏

2. **扩展PhotoLibraryViewController** (2天)
   - 多选模式
   - 选择工具栏

3. **添加分享功能** (1天)
   - 系统分享面板
   - AirDrop支持

### 步骤4: 用户体验优化 (2天)
1. **改进动画和转场** (1天)
2. **完善反馈和错误处理** (1天)

## ✅ 验收标准

### 功能验证
- [x] 相簿管理操作完整
- [x] 搜索功能正常
- [x] 批量操作流畅
- [x] 分享功能正常
- [x] 用户体验良好

### 性能指标
- [x] 搜索响应时间<2秒
- [x] 批量操作无卡顿
- [x] 动画流畅自然
- [x] 内存使用稳定

### 兼容性
- [x] 支持iOS 16.4+
- [x] 适配各种屏幕尺寸
- [x] 支持深色模式
- [x] 无障碍功能支持

---

**第三阶段功能完善后，MPhotos将成为一个功能完整、用户体验优秀的照片管理应用。**