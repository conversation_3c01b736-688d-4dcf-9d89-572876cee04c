//
//  PhotoLibraryService.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import Foundation
import Photos
import UIKit

/// 图片质量级别
enum ImageQualityLevel {
    case fast       // 快速加载，低质量
    case high       // 高质量加载
    case adaptive   // 自适应（根据位置决定）
}
import Darwin

/// 照片数据源协议 - 定义照片数据获取接口
protocol PhotoDataSourceProtocol {
    /// 根据设置获取照片数据
    /// - Parameters:
    ///   - settings: 库设置
    ///   - completion: 完成回调，返回照片模型数组
    func fetchPhotos(with settings: LibrarySettings, completion: @escaping ([PhotoModel]) -> Void)
    
    /// 获取指定位置的照片
    /// - Parameter index: 照片索引
    /// - Returns: PhotoModel或nil
    func fetchPhoto(at index: Int) -> PhotoModel?
    
    /// 总照片数量
    var totalCount: Int { get }
    
    /// 获取照片的缩略图
    /// - Parameters:
    ///   - photo: 照片模型
    ///   - size: 缩略图尺寸
    ///   - completion: 完成回调，返回UIImage或nil
    func fetchThumbnail(for photo: PhotoModel, size: CGSize, completion: @escaping (Result<Any, Error>) -> Void)
}

/// 照片库服务 - 核心照片数据管理服务
class PhotoLibraryService: NSObject, PhotoDataSourceProtocol, PHPhotoLibraryChangeObserver {
    
    // MARK: - 单例
    static let shared = PhotoLibraryService()
    
    // MARK: - 私有属性
    private let imageManager = PHCachingImageManager()
    private var photoAssets: PHFetchResult<PHAsset>?
    var cachedPhotos: [PhotoModel] = []
    private var currentSettings: LibrarySettings?
    
    /// 是否正在加载照片（防重复加载）
    private var isLoadingPhotos: Bool = false

    /// 后台加载任务控制
    private var backgroundLoadingTask: DispatchWorkItem?
    private var currentLoadingSettings: LibrarySettings?

    // 缓存相关
    private var thumbnailCache = NSCache<NSString, UIImage>()  // 内存缓存
    private var activeImageRequests: [String: PHImageRequestID] = [:]

    /// 原始缓存限制（用于防止清理时的限制混乱）
    private var originalCacheLimit: Int = 0

    /// 渐进式加载管理器
    private var progressiveLoadingManager: ProgressiveLoadingManager!
    
    // MARK: - 初始化
    
    override init() {
        super.init()
        setupCache()
        setupImageManager()

        // 初始化渐进式加载管理器
        progressiveLoadingManager = ProgressiveLoadingManager(
            imageManager: imageManager,
            thumbnailCache: thumbnailCache
        )

        // 注册照片库变化监听
        PHPhotoLibrary.shared().register(self)
    }

    deinit {
        // 取消注册照片库变化监听
        PHPhotoLibrary.shared().unregisterChangeObserver(self)
    }
    
    /// 配置缓存策略（渐进式加载优化版）
    private func setupCache() {
        let physicalMemory = ProcessInfo.processInfo.physicalMemory
        let memoryGB = physicalMemory / (1024 * 1024 * 1024)

        // 基于设备内存动态调整缓存策略
        let (memoryLimit, countLimit) = calculateOptimalCacheSettings(deviceMemoryGB: memoryGB)

        print("🔧 设备内存: \(memoryGB)GB，渐进式加载缓存配置: \(memoryLimit/1024/1024)MB")

        thumbnailCache.totalCostLimit = memoryLimit
        thumbnailCache.countLimit = countLimit
        originalCacheLimit = memoryLimit

        // 启用自动清理
        thumbnailCache.evictsObjectsWithDiscardedContent = true

        print("📦 渐进式缓存配置：容量\(memoryLimit/1024/1024)MB，最多\(countLimit)张图片")
    }

    /// 计算最优缓存设置
    private func calculateOptimalCacheSettings(deviceMemoryGB: UInt64) -> (Int, Int) {
        switch deviceMemoryGB {
        case 8...:
            // 8GB+设备：更大缓存，支持更多高分辨率图片
            return (400 * 1024 * 1024, 400)
        case 6..<8:
            // 6-8GB设备：平衡配置
            return (300 * 1024 * 1024, 350)
        case 4..<6:
            // 4-6GB设备：适中配置
            return (250 * 1024 * 1024, 300)
        default:
            // 4GB以下设备：保守配置
            return (200 * 1024 * 1024, 250)
        }
    }
    
    /// 配置图像管理器（原生级别优化）
    private func setupImageManager() {
        // 🚀 原生级别配置
        imageManager.allowsCachingHighQualityImages = true  // 允许高质量缓存
        
        // 设置预加载缓存大小（更大的缓存提升性能）
        _ = 200 * 1024 * 1024  // 200MB PHCaching缓存
        imageManager.stopCachingImagesForAllAssets()  // 清理旧缓存
        
        // print("🖼️ 原生级别图像管理器配置完成：PHCache 200MB")
    }
    

    

    
    // MARK: - 公开属性
    var totalCount: Int {
        return photoAssets?.count ?? 0
    }
    
    // MARK: - PhotoDataSourceProtocol 实现
    
    /// 根据设置获取照片数据（原生照片应用策略：快速返回基础数据）
    func fetchPhotos(with settings: LibrarySettings, completion: @escaping ([PhotoModel]) -> Void) {
        // 🔧 修复：取消之前的后台加载任务，避免冲突
        if let previousTask = backgroundLoadingTask {
            previousTask.cancel()
            backgroundLoadingTask = nil
            print("🛑 取消之前的后台加载任务")
        }

        // 防重复加载检查
        if isLoadingPhotos && currentLoadingSettings == settings {
            // print("⚠️ 相同设置的照片正在加载中，跳过重复请求")
            completion(cachedPhotos)
            return
        }

        // 🔧 修复：如果设置不同，允许重新加载
        if isLoadingPhotos && currentLoadingSettings != settings {
            print("🔄 设置已变更，重新开始照片加载")
        }

        isLoadingPhotos = true
        currentSettings = settings
        currentLoadingSettings = settings
        
        // print("🔄 开始获取照片数据...")
        _ = CFAbsoluteTimeGetCurrent()
        
        // 在后台队列执行照片获取
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            // 创建获取选项
            let fetchOptions = PHFetchOptions()
            
            // 设置排序
            let sortDescriptor: NSSortDescriptor
            switch settings.sortOrder {
            case .newestFirst:
                sortDescriptor = NSSortDescriptor(key: "creationDate", ascending: false)
            case .oldestFirst:
                sortDescriptor = NSSortDescriptor(key: "creationDate", ascending: true)
            }
            fetchOptions.sortDescriptors = [sortDescriptor]
            
            // 关键优化：直接使用PHFetchResult，不转换为数组
            let fetchResult = PHAsset.fetchAssets(with: fetchOptions)
            self.photoAssets = fetchResult
            
            // print("📊 获取到 \(fetchResult.count) 张照片")
            
            // 启动优化：分批创建PhotoModel，根据排序方式调整加载策略
            let totalCount = fetchResult.count
            let initialBatchSize = min(100, totalCount)  // 首批加载100张

            var photoModels: [PhotoModel] = []
            photoModels.reserveCapacity(totalCount)

            // 🔧 修复：根据排序方式选择初始加载策略
            switch settings.sortOrder {
            case .newestFirst:
                // 最新在上：加载前100张（最新的照片）
                for i in 0..<min(initialBatchSize, totalCount) {
                    let asset = fetchResult.object(at: i)
                    let photoModel = PhotoModel(from: asset)
                    photoModels.append(photoModel)
                }
                print("📸 最新在上：加载了前\(photoModels.count)张最新照片")

            case .oldestFirst:
                // 最新在下：加载更多照片，确保包含最新的照片
                // 策略：加载更大的初始批次，或者全部加载（如果数量不是太多）
                let loadCount = min(max(initialBatchSize * 3, totalCount), totalCount)
                for i in 0..<loadCount {
                    let asset = fetchResult.object(at: i)
                    let photoModel = PhotoModel(from: asset)
                    photoModels.append(photoModel)
                }
                print("📸 最新在下：总共\(totalCount)张照片，加载了前\(loadCount)张照片")
            }
            
            // 🔧 修复：只有在设置真正变更时才清空缓存
            if self.currentLoadingSettings != self.currentSettings {
                print("🔄 设置变更，清空之前的缓存")
                self.cachedPhotos.removeAll()
            }

            // 缓存第一批结果，立即显示
            self.cachedPhotos = photoModels
            
            _ = CFAbsoluteTimeGetCurrent()
            // print("⏱️ 首批照片数据获取完成，耗时: \(String(format: "%.2f", endTime - startTime))秒")
            
            // 在主线程回调首批数据
            DispatchQueue.main.async {
                self.isLoadingPhotos = false  // 重置加载标志
                completion(photoModels)
            }
            
            // 后台继续加载剩余照片
            if totalCount > photoModels.count {
                switch settings.sortOrder {
                case .newestFirst:
                    // 最新在上：继续加载后面的照片（较旧的）
                    self.loadRemainingPhotos(fetchResult: fetchResult,
                                           startIndex: initialBatchSize,
                                           totalCount: totalCount,
                                           settings: settings)

                case .oldestFirst:
                    // 最新在下：根据已加载的数量决定是否需要后台加载
                    let loadedCount = self.cachedPhotos.count
                    if loadedCount < totalCount {
                        self.loadRemainingPhotos(fetchResult: fetchResult,
                                               startIndex: loadedCount,
                                               totalCount: totalCount,
                                               settings: settings)
                    }
                }
            }
        }
    }
    
    /// 后台加载剩余照片（分批加载，避免阻塞UI）
    private func loadRemainingPhotos(fetchResult: PHFetchResult<PHAsset>, startIndex: Int, totalCount: Int, settings: LibrarySettings) {
        // 🔧 修复：使用弱引用避免循环引用
        weak var weakTask: DispatchWorkItem?

        let loadingTask = DispatchWorkItem { [weak self] in
            guard let self = self else { return }

            let batchSize = 200  // 每批加载200张
            var currentIndex = startIndex

            while currentIndex < totalCount {
                // 🔧 修复：检查任务是否被取消
                if weakTask?.isCancelled == true {
                    print("🛑 后台照片加载任务被取消，停止加载")
                    return
                }

                // 🔧 修复：检查设置是否仍然匹配
                if self.currentLoadingSettings != settings {
                    print("🛑 设置已变更，停止当前后台加载任务")
                    return
                }

                let batchEndIndex = min(currentIndex + batchSize, totalCount)
                var newPhotos: [PhotoModel] = []

                // 创建当前批次的PhotoModel
                for i in currentIndex..<batchEndIndex {
                    // 再次检查取消状态
                    if weakTask?.isCancelled == true {
                        print("🛑 后台照片加载任务在批次处理中被取消")
                        return
                    }

                    let asset = fetchResult.object(at: i)
                    let photoModel = PhotoModel(from: asset)
                    newPhotos.append(photoModel)
                }

                // 添加到缓存并通知UI更新
                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }

                    // 🔧 修复：再次检查设置一致性
                    if self.currentLoadingSettings == settings && weakTask?.isCancelled != true {
                        // 统一使用追加方式，保持数组顺序稳定
                        self.cachedPhotos.append(contentsOf: newPhotos)
                        // print("📦 后台加载完成 \(batchEndIndex)/\(totalCount) 张照片")

                        // 通知UI更新（通过通知中心）
                        NotificationCenter.default.post(name: NSNotification.Name("PhotosUpdated"), object: nil)
                    } else {
                        print("🛑 设置不匹配或任务已取消，跳过UI更新")
                    }
                }

                currentIndex = batchEndIndex

                // 避免过度占用CPU，每批之间休息一下
                Thread.sleep(forTimeInterval: 0.01)
            }

            // 🔧 修复：任务完成时清理状态
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                if self.currentLoadingSettings == settings && weakTask?.isCancelled != true {
                    print("✅ 所有照片加载完成，共 \(totalCount) 张")
                    self.backgroundLoadingTask = nil
                }
            }
        }

        // 设置弱引用
        weakTask = loadingTask

        // 保存任务引用
        backgroundLoadingTask = loadingTask

        // 在后台队列执行任务
        DispatchQueue.global(qos: .background).async(execute: loadingTask)
    }
    
    /// 取消后台加载任务
    func cancelBackgroundLoading() {
        if let task = backgroundLoadingTask {
            task.cancel()
            backgroundLoadingTask = nil
            print("🛑 手动取消后台照片加载任务")
        }
    }

    /// 检查是否需要恢复后台加载
    func resumeBackgroundLoadingIfNeeded() {
        // 如果没有后台任务在运行，且缓存的照片数量少于总数，则恢复加载
        if backgroundLoadingTask == nil && cachedPhotos.count < totalCount && totalCount > 0 {
            guard let settings = currentLoadingSettings ?? currentSettings,
                  let assets = photoAssets else {
                print("⚠️ 无法恢复后台加载：缺少必要的设置或资源")
                return
            }

            print("🔄 恢复后台照片加载：当前 \(cachedPhotos.count)/\(totalCount) 张")
            loadRemainingPhotos(fetchResult: assets,
                              startIndex: cachedPhotos.count,
                              totalCount: totalCount,
                              settings: settings)
        }
    }

    /// 获取指定位置的照片
    func fetchPhoto(at index: Int) -> PhotoModel? {
        guard index >= 0 && index < cachedPhotos.count else { return nil }
        return cachedPhotos[index]
    }
    
    /// 获取照片的缩略图（新的渐进式加载策略）
    /// - Parameters:
    ///   - photo: 照片模型
    ///   - size: 缩略图尺寸
    ///   - completion: 完成回调（可能被调用多次：先低分辨率，后高分辨率）
    func fetchThumbnail(for photo: PhotoModel, size: CGSize, completion: @escaping (Result<Any, Error>) -> Void) {
        progressiveLoadingManager.loadThumbnailProgressive(for: photo, targetSize: size) { result, isFinal in
            switch result {
            case .success(let image):
                completion(.success(image))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }

    /// 获取照片的缩略图（兼容旧版本API）
    /// - Parameters:
    ///   - photo: 照片模型
    ///   - size: 缩略图尺寸
    ///   - quality: 质量级别（已废弃，保持兼容性）
    ///   - completion: 完成回调
    func fetchThumbnail(for photo: PhotoModel, size: CGSize, quality: ImageQualityLevel = .adaptive, completion: @escaping (Result<Any, Error>) -> Void) {
        // 忽略quality参数，统一使用渐进式加载
        fetchThumbnail(for: photo, size: size, completion: completion)
    }

    /// 取消指定照片的加载请求
    /// - Parameter photoId: 照片ID
    func cancelThumbnailRequest(for photoId: String) {
        progressiveLoadingManager.cancelLoading(for: photoId)
    }

    /// 取消所有活跃的加载请求
    func cancelAllThumbnailRequests() {
        progressiveLoadingManager.cancelAllRequests()
    }


    







    
    /// 图片尺寸调整辅助方法
    private func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage? {
        // 🔧 修复Alpha通道问题：对于照片缩略图，使用不透明上下文
        // 第二个参数设为true表示不透明，避免不必要的Alpha通道
        // 这可以减少文件大小并避免解码时的双倍内存使用
        UIGraphicsBeginImageContextWithOptions(size, true, 0.0)
        defer { UIGraphicsEndImageContext() }

        // 设置白色背景，确保不透明图像的正确渲染
        UIColor.white.setFill()
        UIRectFill(CGRect(origin: .zero, size: size))

        image.draw(in: CGRect(origin: .zero, size: size))
        return UIGraphicsGetImageFromCurrentImageContext()
    }
    
    /// 优化图片尺寸（避免过度请求）- 保留备用
    private func optimizeImageSize(requestedSize: CGSize) -> CGSize {
        let scale = UIScreen.main.scale
        let maxDimension: CGFloat = 400 // 最大尺寸限制
        
        var optimizedSize = CGSize(
            width: requestedSize.width * scale,
            height: requestedSize.height * scale
        )
        
        // 限制最大尺寸，避免内存浪费
        if optimizedSize.width > maxDimension || optimizedSize.height > maxDimension {
            let ratio = min(maxDimension / optimizedSize.width, maxDimension / optimizedSize.height)
            optimizedSize = CGSize(
                width: optimizedSize.width * ratio,
                height: optimizedSize.height * ratio
            )
        }
        
        return optimizedSize
    }
    

    
    // MARK: - 私有辅助方法
    
    /// 根据相册ID获取资源
    private func fetchAssetsForAlbum(identifier: String, options: PHFetchOptions) -> [PHAsset] {
        var assets: [PHAsset] = []
        
        switch identifier {
        case "AllPhotos":
            // 获取所有照片，不限制数量
            let fetchResult = PHAsset.fetchAssets(with: options)
            fetchResult.enumerateObjects { asset, _, _ in
                assets.append(asset)
            }
            
        case "Recents":
            // 获取最近的100张照片（优化启动速度）
            let recentOptions = PHFetchOptions()
            recentOptions.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
            recentOptions.fetchLimit = 100  // 减少到100张
            let recentResult = PHAsset.fetchAssets(with: recentOptions)
            recentResult.enumerateObjects { asset, _, _ in
                assets.append(asset)
            }
            
        case "Favorites":
            let favoriteOptions = PHFetchOptions()
            favoriteOptions.predicate = NSPredicate(format: "isFavorite == YES")
            let fetchResult = PHAsset.fetchAssets(with: favoriteOptions)
            fetchResult.enumerateObjects { asset, _, _ in
                assets.append(asset)
            }
            
        case "Screenshots":
            let screenshotOptions = PHFetchOptions()
            screenshotOptions.predicate = NSPredicate(format: "mediaSubtypes & %d != 0", PHAssetMediaSubtype.photoScreenshot.rawValue)
            let fetchResult = PHAsset.fetchAssets(with: screenshotOptions)
            fetchResult.enumerateObjects { asset, _, _ in
                assets.append(asset)
            }
            
        case "Videos":
            let fetchResult = PHAsset.fetchAssets(with: .video, options: options)
            fetchResult.enumerateObjects { asset, _, _ in
                assets.append(asset)
            }
            
        default:
            // 尝试按照标识符查找用户相册
            let userAlbums = PHAssetCollection.fetchAssetCollections(with: .album, subtype: .any, options: nil)
            userAlbums.enumerateObjects { collection, _, stop in
                if collection.localIdentifier == identifier {
                    let fetchResult = PHAsset.fetchAssets(in: collection, options: options)
                    fetchResult.enumerateObjects { asset, _, _ in
                        assets.append(asset)
                    }
                    stop.pointee = true
                }
            }
        }
        
        return assets
    }
    
    /// 去除重复的Asset并按指定顺序排序
    private func removeDuplicateAssets(_ assets: [PHAsset], sortOrder: LibrarySettings.SortOrder) -> [PHAsset] {
        // 使用Set去重
        let uniqueAssets = Array(Set(assets))
        
        // 按创建日期排序
        return uniqueAssets.sorted { asset1, asset2 in
            let date1 = asset1.creationDate ?? Date.distantPast
            let date2 = asset2.creationDate ?? Date.distantPast
            
            switch sortOrder {
            case .newestFirst:
                return date1 > date2
            case .oldestFirst:
                return date1 < date2
            }
        }
    }
    
    // MARK: - 缓存管理
    
    /// 开始缓存指定照片的缩略图（优化版）
    /// - Parameters:
    ///   - photos: 要缓存的照片数组
    ///   - size: 缩略图尺寸
    func startCaching(for photos: [PhotoModel], targetSize size: CGSize) {
        let optimizedSize = optimizeImageSize(requestedSize: size)
        let assets = photos.map { $0.asset }
        
        let options = PHImageRequestOptions()
        // 预加载使用快速模式
        options.deliveryMode = .fastFormat
        options.resizeMode = .fast
        options.isNetworkAccessAllowed = false  // 预加载不使用网络
        options.isSynchronous = false
        
        imageManager.startCachingImages(for: assets, 
                                      targetSize: optimizedSize, 
                                      contentMode: .aspectFill, 
                                      options: options)
        
        // print("🚀 开始预加载 \(photos.count) 张图片，尺寸: \(optimizedSize)")
    }
    
    /// 停止缓存指定照片的缩略图
    /// - Parameters:
    ///   - photos: 要停止缓存的照片数组
    ///   - size: 缩略图尺寸
    func stopCaching(for photos: [PhotoModel], targetSize size: CGSize) {
        let optimizedSize = optimizeImageSize(requestedSize: size)
        let assets = photos.map { $0.asset }
        
        imageManager.stopCachingImages(for: assets, 
                                     targetSize: optimizedSize, 
                                     contentMode: .aspectFill, 
                                     options: nil)
        
        // print("🛑 停止预加载 \(photos.count) 张图片")
    }
    
    /// 停止所有缓存
    func stopAllCaching() {
        imageManager.stopCachingImagesForAllAssets()
    }

    /// 智能清理内存缓存（布局切换时使用）
    func clearMemoryCache() {
        print("🧹 开始智能清理内存缓存（渐进式加载版本）")

        // 1. 先进行智能清理
        smartCleanupCache()

        // 2. 清理PHImageManager的系统缓存
        imageManager.stopCachingImagesForAllAssets()

        // 3. 取消所有渐进式加载请求
        progressiveLoadingManager.cancelAllRequests()

        // 4. 清理旧的activeImageRequests（向后兼容）
        for (_, requestID) in activeImageRequests {
            imageManager.cancelImageRequest(requestID)
        }
        activeImageRequests.removeAll()

        print("✅ 渐进式加载缓存清理完成，活跃请求数: \(progressiveLoadingManager.getActiveRequestCount())")
    }

    /// 强制清理所有缓存（内存警告时使用）
    func forceCleanAllCache() {
        print("🚨 强制清理所有缓存")

        // 1. 清空NSCache
        thumbnailCache.removeAllObjects()

        // 2. 清理PHImageManager缓存
        imageManager.stopCachingImagesForAllAssets()

        // 3. 取消所有请求
        for (_, requestID) in activeImageRequests {
            imageManager.cancelImageRequest(requestID)
        }
        activeImageRequests.removeAll()

        print("✅ 强制清理完成")
    }

    /// 获取缓存键（供外部压缩检测使用）
    func getCacheKey(for photoId: String, requestedSize: CGFloat) -> NSString {
        let scale = UIScreen.main.scale

        // 简化的缓存键生成逻辑
        if requestedSize <= 150 * scale {
            return "\(photoId)_small_highres" as NSString
        } else if requestedSize <= 250 * scale {
            return "\(photoId)_medium_highres" as NSString
        } else {
            return "\(photoId)_large_highres" as NSString
        }
    }

    /// 获取缓存的图片（供外部压缩检测使用）
    func getCachedImage(for cacheKey: NSString) -> UIImage? {
        return thumbnailCache.object(forKey: cacheKey)
    }

    /// 智能缓存清理策略
    private func smartCleanupCache() {
        // 获取当前内存使用情况
        let currentMemoryUsage = getCurrentMemoryUsage()
        let memoryLimit = thumbnailCache.totalCostLimit
        let currentMemoryMB = currentMemoryUsage / 1024 / 1024
        let limitMB = memoryLimit / 1024 / 1024

        print("🧹 智能缓存清理：当前使用 \(currentMemoryMB)MB / 限制 \(limitMB)MB")

        // 🔧 修复：更激进的清理策略，特别针对十列布局
        let shouldCleanup = currentMemoryUsage > Int(Double(memoryLimit) * 0.5) || // 50%就开始清理
                           currentMemoryMB > 250 || // 超过250MB绝对值清理
                           (currentMemoryMB > 150) // 超过150MB就清理

        if shouldCleanup {
            print("🧹 触发清理：内存使用 \(currentMemoryMB)MB，开始清理缓存")
            cleanupLeastRecentlyUsed()

            // 🔧 新增：如果内存使用过高，强制清理
            if currentMemoryMB > 400 {
                print("🚨 内存使用过高(\(currentMemoryMB)MB)，执行强制清理")
                thumbnailCache.removeAllObjects()
                imageManager.stopCachingImagesForAllAssets()
            }
        } else {
            print("🧹 内存使用正常：\(currentMemoryMB)MB，无需清理")
        }
    }

    /// 检测是否可能是十列布局（基于缓存的图片尺寸特征）
    private func isLikelyTenColumnLayout() -> Bool {
        // 简单启发式：如果最近请求的都是小尺寸图片，可能是十列布局
        // 这里可以根据实际情况调整检测逻辑
        return true  // 暂时总是返回true，确保清理策略生效
    }

    /// 检查内存使用并在需要时清理
    private func checkAndCleanupMemoryIfNeeded() {
        let currentMemoryUsage = getCurrentMemoryUsage()
        let currentMemoryMB = currentMemoryUsage / 1024 / 1024

        // 🔧 激进的内存管理：超过200MB就开始清理
        if currentMemoryMB > 200 {
            print("🧹 主动内存检查：当前使用 \(currentMemoryMB)MB，触发清理")
            smartCleanupCache()
        }

        // 🔧 紧急清理：超过400MB强制清理
        if currentMemoryMB > 400 {
            print("🚨 紧急内存清理：当前使用 \(currentMemoryMB)MB，强制清理")
            thumbnailCache.removeAllObjects()
            imageManager.stopCachingImagesForAllAssets()
        }
    }

    /// 清理最少使用的缓存项
    private func cleanupLeastRecentlyUsed() {
        // 🔧 修复：使用保存的原始限制，避免基于已修改的限制进行计算
        guard originalCacheLimit > 0 else {
            print("⚠️ 原始缓存限制未设置，跳过清理")
            return
        }

        let targetReduction = originalCacheLimit / 4  // 清理25%

        // 简单策略：临时降低到原始限制的一半，让NSCache自动清理
        thumbnailCache.totalCostLimit = originalCacheLimit / 2

        // 等待NSCache自动清理后恢复到原始限制
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            guard let self = self else { return }
            // 🔧 关键修复：始终恢复到原始限制，而不是当前限制
            self.thumbnailCache.totalCostLimit = self.originalCacheLimit
            print("✅ 缓存限制已恢复到原始值: \(self.originalCacheLimit/1024/1024)MB")
        }

        print("🧹 LRU清理完成，释放约 \(targetReduction/1024/1024)MB 内存")
    }

    /// 获取当前内存使用估算
    private func getCurrentMemoryUsage() -> Int {
        // 🔧 修复：使用实际的应用内存使用量
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }

        if kerr == KERN_SUCCESS {
            return Int(info.resident_size)  // 返回字节数
        } else {
            print("⚠️ 获取内存使用量失败，使用缓存估算")
            // 降级方案：估算缓存使用量
            return estimateCacheMemoryUsage()
        }
    }

    /// 估算缓存内存使用量
    private func estimateCacheMemoryUsage() -> Int {
        // 简单估算：假设平均每张图片的实际内存占用
        let averageCostPerImage: Int
        let physicalMemory = ProcessInfo.processInfo.physicalMemory

        if physicalMemory >= 6 * 1024 * 1024 * 1024 {
            averageCostPerImage = 2 * 1024 * 1024  // 2MB per image
        } else {
            averageCostPerImage = 1 * 1024 * 1024  // 1MB per image
        }

        // 估算当前缓存的图片数量（这是个粗略估算）
        let estimatedImageCount = min(200, thumbnailCache.countLimit / 4)
        return estimatedImageCount * averageCostPerImage
    }



    /// 计算图片的实际内存成本
    /// - Parameter image: UIImage对象
    /// - Returns: 估算的内存占用字节数
    private func calculateImageMemoryCost(_ image: UIImage) -> Int {
        let pixelCount = Int(image.size.width * image.size.height)

        // 考虑不同的图片格式和压缩
        let bytesPerPixel: Int
        if let cgImage = image.cgImage {
            let bitsPerPixel = cgImage.bitsPerPixel
            bytesPerPixel = max(bitsPerPixel / 8, 4) // 至少按4字节计算
        } else {
            bytesPerPixel = 4 // 默认RGBA
        }

        // 基础像素数据 + UIImage对象开销 + 可能的解压缩开销
        let pixelDataCost = pixelCount * bytesPerPixel
        let objectOverhead = 1024 // UIImage对象本身的开销
        let decompressionOverhead = pixelDataCost / 4 // 解压缩可能的额外开销

        let totalCost = pixelDataCost + objectOverhead + decompressionOverhead

        // 限制最大成本，避免异常值
        return min(totalCost, 10 * 1024 * 1024) // 最大10MB
    }

    /// 获取缓存状态信息（用于调试）
    func getCacheStatus() -> String {
        let memoryLimit = thumbnailCache.totalCostLimit
        let countLimit = thumbnailCache.countLimit
        let estimatedUsage = getCurrentMemoryUsage()

        return """
        📊 缓存状态:
        - 内存限制: \(memoryLimit/1024/1024)MB
        - 数量限制: \(countLimit)张
        - 估算使用: \(estimatedUsage/1024/1024)MB
        - 使用率: \(Int(Double(estimatedUsage)/Double(memoryLimit)*100))%
        """
    }
}

// MARK: - 错误定义

/// 照片库服务错误枚举
enum PhotoLibraryError: Error, LocalizedError {
    case permissionDenied           // 权限被拒绝
    case imageLoadFailed           // 图片加载失败  
    case assetNotFound            // 资源未找到
    case invalidSettings          // 无效的设置
    
    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "访问照片库权限被拒绝"
        case .imageLoadFailed:
            return "图片加载失败"
        case .assetNotFound:
            return "找不到指定的照片资源"
        case .invalidSettings:
            return "无效的库设置"
        }
    }
}

// MARK: - PHPhotoLibraryChangeObserver

extension PhotoLibraryService {

    func photoLibraryDidChange(_ changeInstance: PHChange) {
        // 检查是否有照片资源的变化
        guard let photoAssets = photoAssets,
              let changeDetails = changeInstance.changeDetails(for: photoAssets) else {
            return
        }

        // 在主线程处理UI更新
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            if changeDetails.hasIncrementalChanges {
                // 有增量变化，更新数据
                self.photoAssets = changeDetails.fetchResultAfterChanges

                // 如果有新增的照片，重新加载数据
                if !changeDetails.insertedObjects.isEmpty {
                    print("📸 检测到新增照片: \(changeDetails.insertedObjects.count) 张")
                    self.refreshPhotosForNewContent()
                }

                // 如果有删除的照片，重新加载数据
                if !changeDetails.removedObjects.isEmpty {
                    print("🗑️ 检测到删除照片: \(changeDetails.removedObjects.count) 张")
                    self.refreshPhotosForNewContent()
                }
            } else {
                // 大量变化，完全重新加载
                print("🔄 照片库发生大量变化，重新加载")
                self.photoAssets = changeDetails.fetchResultAfterChanges
                self.refreshPhotosForNewContent()
            }
        }
    }

    /// 为新内容刷新照片数据
    private func refreshPhotosForNewContent() {
        // 清空当前缓存
        cachedPhotos.removeAll()

        // 重新加载照片数据
        if let currentSettings = currentLoadingSettings {
            fetchPhotos(with: currentSettings) { _ in
                DispatchQueue.main.async {
                    // 通知UI更新
                    NotificationCenter.default.post(name: NSNotification.Name("PhotosUpdated"), object: nil)
                }
            }
        }
    }






}
