//
//  ProgressiveLoadingManager.swift
//  MPhotos
//
//  Created for progressive image loading without disk cache
//

import Foundation
import Photos
import UIKit

/// 渐进式加载管理器 - 实现两阶段图片加载策略
class ProgressiveLoadingManager {
    
    // MARK: - 属性
    
    private let imageManager: PHCachingImageManager
    private let thumbnailCache: NSCache<NSString, UIImage>
    private var activeRequests: [String: [PHImageRequestID]] = [:]
    
    // MARK: - 初始化
    
    init(imageManager: PHCachingImageManager, thumbnailCache: NSCache<NSString, UIImage>) {
        self.imageManager = imageManager
        self.thumbnailCache = thumbnailCache
    }
    
    // MARK: - 公开方法
    
    /// 渐进式加载缩略图
    /// - Parameters:
    ///   - photo: 照片模型
    ///   - targetSize: 目标尺寸
    ///   - completion: 完成回调（可能被调用多次）
    func loadThumbnailProgressive(for photo: PhotoModel, 
                                targetSize: CGSize,
                                completion: @escaping (Result<UIImage, Error>, Bool) -> Void) {
        
        let photoId = photo.id
        let requestedDimension = max(targetSize.width, targetSize.height)
        
        // 生成缓存键
        let highResCacheKey = generateCacheKey(for: photoId, dimension: requestedDimension, isLowRes: false)
        let lowResCacheKey = generateCacheKey(for: photoId, dimension: requestedDimension, isLowRes: true)
        
        // 1. 检查高分辨率缓存
        if let highResImage = thumbnailCache.object(forKey: highResCacheKey) {
            completion(.success(highResImage), true) // true表示最终版本
            return
        }
        
        // 2. 检查低分辨率缓存
        if let lowResImage = thumbnailCache.object(forKey: lowResCacheKey) {
            completion(.success(lowResImage), false) // false表示临时版本
            // 继续加载高分辨率版本
            loadHighResolution(for: photo, targetSize: targetSize, 
                             highResCacheKey: highResCacheKey, completion: completion)
            return
        }
        
        // 3. 两阶段加载
        startProgressiveLoading(for: photo, targetSize: targetSize,
                              lowResCacheKey: lowResCacheKey,
                              highResCacheKey: highResCacheKey,
                              completion: completion)
    }
    
    /// 取消指定照片的加载请求
    /// - Parameter photoId: 照片ID
    func cancelLoading(for photoId: String) {
        if let requestIDs = activeRequests[photoId] {
            for requestID in requestIDs {
                imageManager.cancelImageRequest(requestID)
            }
            activeRequests.removeValue(forKey: photoId)
        }
    }
    
    // MARK: - 私有方法
    
    /// 开始渐进式加载
    private func startProgressiveLoading(for photo: PhotoModel,
                                       targetSize: CGSize,
                                       lowResCacheKey: NSString,
                                       highResCacheKey: NSString,
                                       completion: @escaping (Result<UIImage, Error>, Bool) -> Void) {
        
        let photoId = photo.id
        var requestIDs: [PHImageRequestID] = []
        
        // 第一阶段：加载低分辨率版本
        let lowResSize = CGSize(width: targetSize.width * 0.5, height: targetSize.height * 0.5)
        let lowResOptions = createLowResOptions()
        
        let lowResRequestID = imageManager.requestImage(
            for: photo.asset,
            targetSize: lowResSize,
            contentMode: .aspectFill,
            options: lowResOptions
        ) { [weak self] image, info in
            guard let self = self else { return }

            // 🔧 增强错误处理
            if let image = image {
                // 缓存低分辨率版本
                let cost = self.calculateImageMemoryCost(image)
                self.thumbnailCache.setObject(image, forKey: lowResCacheKey, cost: cost)

                // 立即返回低分辨率版本
                completion(.success(image), false)

                // 开始加载高分辨率版本
                self.loadHighResolution(for: photo, targetSize: targetSize,
                                      highResCacheKey: highResCacheKey, completion: completion)
            } else {
                // 处理低分辨率加载失败的情况
                self.handleImageLoadError(info: info, photoId: photoId, isLowRes: true, completion: completion)
            }
        }
        
        requestIDs.append(lowResRequestID)
        activeRequests[photoId] = requestIDs
    }
    
    /// 加载高分辨率版本
    private func loadHighResolution(for photo: PhotoModel,
                                  targetSize: CGSize,
                                  highResCacheKey: NSString,
                                  completion: @escaping (Result<UIImage, Error>, Bool) -> Void) {
        
        let photoId = photo.id
        let highResOptions = createHighResOptions()
        
        let highResRequestID = imageManager.requestImage(
            for: photo.asset,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: highResOptions
        ) { [weak self] image, info in
            guard let self = self else { return }

            // 🔧 增强错误处理
            if let image = image {
                // 缓存高分辨率版本
                let cost = self.calculateImageMemoryCost(image)
                self.thumbnailCache.setObject(image, forKey: highResCacheKey, cost: cost)

                // 清理对应的低分辨率版本以节省内存
                self.cleanupLowResVersion(for: photoId, targetDimension: max(targetSize.width, targetSize.height))

                // 返回高分辨率版本
                completion(.success(image), true)

                // 清理请求记录
                self.activeRequests.removeValue(forKey: photoId)
            } else {
                // 处理高分辨率加载失败的情况
                self.handleImageLoadError(info: info, photoId: photoId, isLowRes: false, completion: completion)

                // 清理请求记录
                self.activeRequests.removeValue(forKey: photoId)
            }
        }
        
        // 添加到活跃请求列表
        if activeRequests[photoId] != nil {
            activeRequests[photoId]?.append(highResRequestID)
        } else {
            activeRequests[photoId] = [highResRequestID]
        }
    }
    
    /// 创建低分辨率请求选项
    private func createLowResOptions() -> PHImageRequestOptions {
        let options = PHImageRequestOptions()
        options.deliveryMode = .fastFormat
        options.resizeMode = .fast
        options.isNetworkAccessAllowed = false  // 仅本地，快速响应
        options.isSynchronous = false
        return options
    }
    
    /// 创建高分辨率请求选项
    private func createHighResOptions() -> PHImageRequestOptions {
        let options = PHImageRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.resizeMode = .exact
        options.isNetworkAccessAllowed = true
        options.isSynchronous = false
        return options
    }
    
    /// 生成缓存键
    private func generateCacheKey(for photoId: String, dimension: CGFloat, isLowRes: Bool) -> NSString {
        let scale = UIScreen.main.scale
        let resSuffix = isLowRes ? "_lowres" : "_highres"
        
        if dimension <= 150 * scale {
            return "\(photoId)_small\(resSuffix)" as NSString
        } else if dimension <= 250 * scale {
            return "\(photoId)_medium\(resSuffix)" as NSString
        } else {
            return "\(photoId)_large\(resSuffix)" as NSString
        }
    }
    
    /// 计算图片内存成本
    private func calculateImageMemoryCost(_ image: UIImage) -> Int {
        let pixelCount = Int(image.size.width * image.size.height)
        let bytesPerPixel = 4 // RGBA
        let pixelDataCost = pixelCount * bytesPerPixel
        let objectOverhead = 1024
        let decompressionOverhead = pixelDataCost / 4
        
        let totalCost = pixelDataCost + objectOverhead + decompressionOverhead
        return min(totalCost, 10 * 1024 * 1024) // 最大10MB
    }
    
    /// 清理低分辨率版本以节省内存
    private func cleanupLowResVersion(for photoId: String, targetDimension: CGFloat) {
        let lowResCacheKey = generateCacheKey(for: photoId, dimension: targetDimension, isLowRes: true)
        thumbnailCache.removeObject(forKey: lowResCacheKey)
    }

    /// 取消所有活跃的加载请求
    func cancelAllRequests() {
        for (_, requestIDs) in activeRequests {
            for requestID in requestIDs {
                imageManager.cancelImageRequest(requestID)
            }
        }
        activeRequests.removeAll()
    }

    /// 获取当前活跃请求数量（用于调试）
    func getActiveRequestCount() -> Int {
        return activeRequests.values.reduce(0) { $0 + $1.count }
    }

    /// 处理图片加载错误
    private func handleImageLoadError(info: [AnyHashable: Any]?,
                                    photoId: String,
                                    isLowRes: Bool,
                                    completion: @escaping (Result<UIImage, Error>, Bool) -> Void) {

        let resolutionType = isLowRes ? "低分辨率" : "高分辨率"

        // 检查是否有具体的错误信息
        if let error = info?[PHImageErrorKey] as? Error {
            #if DEBUG
            if let phError = error as? PHPhotosError {
                let diagnostic = SystemErrorDiagnostics.shared.diagnosePHPhotosError(phError)
                print("📸 \(resolutionType)图片加载错误诊断:")
                print("   照片ID: \(photoId)")
                print("   错误代码: \(diagnostic.errorCode)")
                print("   描述: \(diagnostic.description)")
                print("   可能原因: \(diagnostic.possibleCauses.joined(separator: ", "))")
                print("   建议解决方案: \(diagnostic.solutions.joined(separator: ", "))")
            } else {
                print("⚠️ \(resolutionType)图片加载失败: \(error.localizedDescription)")
                print("   照片ID: \(photoId)")
            }
            #endif

            // 对于高分辨率加载失败，不报告错误（因为可能已经有低分辨率版本）
            if !isLowRes {
                // 高分辨率失败时不调用completion，保留低分辨率版本
                return
            }

            completion(.failure(error), true)
        } else {
            // 没有具体错误信息的情况
            #if DEBUG
            print("⚠️ \(resolutionType)图片加载失败：无图片返回且无具体错误信息")
            print("   照片ID: \(photoId)")
            if let info = info {
                print("   附加信息: \(info)")
            }
            #endif

            let customError = NSError(domain: "ProgressiveLoadingError",
                                    code: 3303,
                                    userInfo: [NSLocalizedDescriptionKey: "\(resolutionType)图片加载失败"])

            // 对于高分辨率加载失败，不报告错误
            if !isLowRes {
                return
            }

            completion(.failure(customError), true)
        }
    }
}
