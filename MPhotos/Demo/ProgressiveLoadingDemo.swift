//
//  ProgressiveLoadingDemo.swift
//  MPhotos
//
//  Created for demonstrating progressive loading functionality
//

import UIKit
import Photos

/// 渐进式加载演示控制器
class ProgressiveLoadingDemo: UIViewController {
    
    // MARK: - UI组件
    
    private let imageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .systemGray6
        imageView.layer.borderColor = UIColor.systemBlue.cgColor
        imageView.layer.borderWidth = 2
        imageView.translatesAutoresizingMaskIntoConstraints = false
        return imageView
    }()
    
    private let statusLabel: UILabel = {
        let label = UILabel()
        label.text = "准备加载..."
        label.textAlignment = .center
        label.numberOfLines = 0
        label.font = UIFont.systemFont(ofSize: 16)
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private let loadButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("开始渐进式加载演示", for: .normal)
        button.titleLabel?.font = UIFont.boldSystemFont(ofSize: 18)
        button.backgroundColor = .systemBlue
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }()
    
    private let memoryLabel: UILabel = {
        let label = UILabel()
        label.text = "内存使用: --MB"
        label.textAlignment = .center
        label.font = UIFont.monospacedSystemFont(ofSize: 14, weight: .medium)
        label.textColor = .systemGreen
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    // MARK: - 属性
    
    private var memoryTimer: Timer?
    private var loadingStartTime: CFAbsoluteTime = 0
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupActions()
        startMemoryMonitoring()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        memoryTimer?.invalidate()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        title = "渐进式加载演示"
        view.backgroundColor = .systemBackground
        
        view.addSubview(imageView)
        view.addSubview(statusLabel)
        view.addSubview(loadButton)
        view.addSubview(memoryLabel)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 图片视图
            imageView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
            imageView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            imageView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            imageView.heightAnchor.constraint(equalTo: imageView.widthAnchor),
            
            // 状态标签
            statusLabel.topAnchor.constraint(equalTo: imageView.bottomAnchor, constant: 20),
            statusLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            statusLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            
            // 内存标签
            memoryLabel.topAnchor.constraint(equalTo: statusLabel.bottomAnchor, constant: 10),
            memoryLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            memoryLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            
            // 加载按钮
            loadButton.topAnchor.constraint(equalTo: memoryLabel.bottomAnchor, constant: 30),
            loadButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            loadButton.widthAnchor.constraint(equalToConstant: 250),
            loadButton.heightAnchor.constraint(equalToConstant: 50)
        ])
    }
    
    private func setupActions() {
        loadButton.addTarget(self, action: #selector(startProgressiveLoadingDemo), for: .touchUpInside)
    }
    
    // MARK: - 演示方法
    
    @objc private func startProgressiveLoadingDemo() {
        loadButton.isEnabled = false
        loadingStartTime = CFAbsoluteTimeGetCurrent()
        statusLabel.text = "正在获取照片..."
        imageView.image = nil
        
        // 获取第一张照片进行演示
        requestPhotoLibraryAccess { [weak self] in
            self?.loadFirstPhoto()
        }
    }
    
    private func requestPhotoLibraryAccess(completion: @escaping () -> Void) {
        PHPhotoLibrary.requestAuthorization { status in
            DispatchQueue.main.async {
                switch status {
                case .authorized, .limited:
                    completion()
                default:
                    self.statusLabel.text = "需要照片库访问权限"
                    self.loadButton.isEnabled = true
                }
            }
        }
    }
    
    private func loadFirstPhoto() {
        let fetchOptions = PHFetchOptions()
        fetchOptions.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
        fetchOptions.fetchLimit = 1
        
        let fetchResult = PHAsset.fetchAssets(with: fetchOptions)
        
        guard let asset = fetchResult.firstObject else {
            statusLabel.text = "没有找到照片"
            loadButton.isEnabled = true
            return
        }
        
        let photo = PhotoModel(from: asset)
        let targetSize = CGSize(width: 300, height: 300)
        
        statusLabel.text = "开始渐进式加载...\n第一阶段：低分辨率"
        
        var callbackCount = 0
        
        // 使用渐进式加载
        PhotoLibraryService.shared.fetchThumbnail(for: photo, size: targetSize) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }
                
                callbackCount += 1
                let currentTime = CFAbsoluteTimeGetCurrent()
                let elapsedTime = currentTime - self.loadingStartTime
                
                switch result {
                case .success(let image):
                    if let uiImage = image as? UIImage {
                        if callbackCount == 1 {
                            // 第一次回调：低分辨率
                            self.imageView.image = uiImage
                            self.statusLabel.text = """
                            第一阶段完成 ✅
                            低分辨率图片已显示
                            耗时: \(String(format: "%.2f", elapsedTime))秒
                            等待高分辨率版本...
                            """
                        } else {
                            // 第二次回调：高分辨率
                            UIView.transition(with: self.imageView, duration: 0.2, 
                                            options: .transitionCrossDissolve) {
                                self.imageView.image = uiImage
                            }
                            
                            self.statusLabel.text = """
                            渐进式加载完成 🎉
                            
                            第一阶段: 低分辨率 (快速显示)
                            第二阶段: 高分辨率 (质量提升)
                            
                            总耗时: \(String(format: "%.2f", elapsedTime))秒
                            回调次数: \(callbackCount)次
                            """
                            
                            self.loadButton.isEnabled = true
                        }
                    }
                case .failure(let error):
                    self.statusLabel.text = "加载失败: \(error.localizedDescription)"
                    self.loadButton.isEnabled = true
                }
            }
        }
    }
    
    // MARK: - 内存监控
    
    private func startMemoryMonitoring() {
        memoryTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateMemoryUsage()
        }
    }
    
    private func updateMemoryUsage() {
        let memoryUsage = getCurrentMemoryUsage() / 1024 / 1024
        memoryLabel.text = "内存使用: \(memoryUsage)MB"
    }
}

// MARK: - 内存使用工具函数

private func getCurrentMemoryUsage() -> Int {
    var info = mach_task_basic_info()
    var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
    
    let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
        $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
            task_info(mach_task_self_,
                     task_flavor_t(MACH_TASK_BASIC_INFO),
                     $0,
                     &count)
        }
    }
    
    if kerr == KERN_SUCCESS {
        return Int(info.resident_size)
    } else {
        return 0
    }
}
