//
//  CacheMonitor.swift
//  MPhotos
//
//  Created for monitoring cache performance
//

import Foundation
import UIKit

/// 缓存性能监控器
class CacheMonitor {
    static let shared = CacheMonitor()
    
    private var startTime: CFAbsoluteTime = 0
    private var lastMemoryCheck: CFAbsoluteTime = 0
    private let checkInterval: CFAbsoluteTime = 5.0 // 5秒检查一次
    
    private init() {}
    
    /// 开始监控
    func startMonitoring() {
        startTime = CFAbsoluteTimeGetCurrent()
        lastMemoryCheck = startTime
        
        // 定期检查内存使用
        Timer.scheduledTimer(withTimeInterval: checkInterval, repeats: true) { [weak self] _ in
            self?.checkMemoryUsage()
        }
        
        print("🔍 缓存监控已启动")
    }
    
    /// 检查内存使用情况
    private func checkMemoryUsage() {
        let currentTime = CFAbsoluteTimeGetCurrent()
        let memoryUsage = getMemoryUsage()
        
        print("📊 [⏰\(String(format: "%.1f", currentTime - startTime))s] 内存使用: \(memoryUsage)MB")
        
        // 获取缓存状态
        let cacheStatus = PhotoLibraryService.shared.getCacheStatus()
        print(cacheStatus)
        
        // 如果内存使用过高，发出警告
        if memoryUsage > 300 {
            print("⚠️ 内存使用过高: \(memoryUsage)MB，建议清理缓存")
        }
        
        lastMemoryCheck = currentTime
    }
    
    /// 获取当前内存使用量（MB）- 线程安全版本
    private func getMemoryUsage() -> Int {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }

        if kerr == KERN_SUCCESS {
            return Int(info.resident_size) / 1024 / 1024
        } else {
            print("⚠️ 获取内存使用量失败: \(kerr)")
            return 0
        }
    }
    
    /// 记录缓存操作
    func logCacheOperation(_ operation: String, details: String = "") {
        let currentTime = CFAbsoluteTimeGetCurrent()
        let elapsed = currentTime - startTime
        
        print("🗂️ [⏰\(String(format: "%.3f", elapsed))s] \(operation) \(details)")
    }
    
    /// 记录布局切换
    func logLayoutSwitch(from: String, to: String) {
        let memoryBefore = getMemoryUsage()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            let memoryAfter = self?.getMemoryUsage() ?? 0
            let memoryDiff = memoryAfter - memoryBefore
            
            print("🔄 布局切换: \(from) → \(to)")
            print("   内存变化: \(memoryBefore)MB → \(memoryAfter)MB (\(memoryDiff > 0 ? "+" : "")\(memoryDiff)MB)")
        }
    }
    
    /// 生成性能报告
    func generateReport() -> String {
        let currentTime = CFAbsoluteTimeGetCurrent()
        let totalTime = currentTime - startTime
        let currentMemory = getMemoryUsage()
        
        let report = """
        
        📈 缓存性能报告
        ================
        运行时间: \(String(format: "%.1f", totalTime))秒
        当前内存: \(currentMemory)MB
        
        \(PhotoLibraryService.shared.getCacheStatus())
        
        💡 优化建议:
        - 如果内存使用 > 200MB，考虑降低缓存限制
        - 如果频繁清理缓存，考虑增加内存限制
        - 监控布局切换时的内存变化
        ================
        
        """
        
        return report
    }
}

// MARK: - 扩展：添加到PhotoLibraryService
extension PhotoLibraryService {
    /// 监控缓存操作
    func logCacheOperation(_ operation: String, details: String = "") {
        #if DEBUG
        CacheMonitor.shared.logCacheOperation(operation, details: details)
        #endif
    }
}
