//
//  SystemErrorDiagnostics.swift
//  MPhotos
//
//  Created for diagnosing system-level errors
//

#if DEBUG

import Foundation
import Photos
import UIKit

/// 系统错误诊断工具
class SystemErrorDiagnostics {
    
    static let shared = SystemErrorDiagnostics()
    
    private init() {}
    
    // MARK: - 诊断方法
    
    /// 执行完整的系统诊断
    func performFullDiagnostics() -> SystemDiagnosticResult {
        var issues: [String] = []
        var solutions: [String] = []
        var successes: [String] = []
        
        // 1. 检查照片库访问权限
        let authStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        switch authStatus {
        case .authorized:
            successes.append("✅ 照片库访问权限已授权")
        case .limited:
            issues.append("⚠️ 照片库访问权限受限")
            solutions.append("💡 建议：在设置中允许访问所有照片")
        case .denied:
            issues.append("❌ 照片库访问权限被拒绝")
            solutions.append("💡 解决：前往设置 > 隐私与安全性 > 照片，允许MPhotos访问")
        case .notDetermined:
            issues.append("⚠️ 照片库访问权限未确定")
            solutions.append("💡 解决：重新启动应用并授权照片访问")
        case .restricted:
            issues.append("❌ 照片库访问权限受限制")
            solutions.append("💡 解决：检查家长控制或企业策略设置")
        @unknown default:
            issues.append("❓ 未知的照片库权限状态")
        }
        
        // 2. 检查照片库可用性
        let photoCount = PHAsset.fetchAssets(with: nil).count
        if photoCount > 0 {
            successes.append("✅ 照片库包含 \(photoCount) 张照片")
        } else {
            issues.append("⚠️ 照片库为空或无法访问")
            solutions.append("💡 建议：检查照片库中是否有照片，或尝试重新授权")
        }
        
        // 3. 检查设备存储空间
        if let availableSpace = getAvailableStorageSpace() {
            let spaceGB = Double(availableSpace) / (1024 * 1024 * 1024)
            if spaceGB > 1.0 {
                successes.append("✅ 可用存储空间: \(String(format: "%.1f", spaceGB)) GB")
            } else {
                issues.append("⚠️ 存储空间不足: \(String(format: "%.1f", spaceGB)) GB")
                solutions.append("💡 建议：清理设备存储空间")
            }
        }
        
        // 4. 检查内存使用情况
        let memoryInfo = getMemoryUsage()
        successes.append("✅ 当前内存使用: \(String(format: "%.1f", memoryInfo.used)) MB")
        if memoryInfo.used > 500 {
            issues.append("⚠️ 内存使用较高: \(String(format: "%.1f", memoryInfo.used)) MB")
            solutions.append("💡 建议：重启应用或设备以释放内存")
        }
        
        // 5. 检查网络连接（如果需要iCloud同步）
        if isNetworkAvailable() {
            successes.append("✅ 网络连接正常")
        } else {
            issues.append("⚠️ 网络连接不可用")
            solutions.append("💡 建议：检查WiFi或蜂窝网络连接")
        }
        
        return SystemDiagnosticResult(
            successes: successes,
            issues: issues,
            solutions: solutions,
            timestamp: Date()
        )
    }
    
    /// 诊断PHPhotos错误
    func diagnosePHPhotosError(_ error: Error) -> PHErrorDiagnostic {
        guard let phError = error as? PHPhotosError else {
            return PHErrorDiagnostic(
                errorCode: -1,
                description: "非PHPhotos错误",
                possibleCauses: ["系统级错误"],
                solutions: ["检查系统日志获取更多信息"]
            )
        }
        
        let errorCode = phError.code.rawValue
        var description = ""
        var causes: [String] = []
        var solutions: [String] = []
        
        switch phError.code {
        case .libraryVolumeOffline:
            description = "照片库卷离线"
            causes = ["iCloud照片库同步问题", "网络连接问题"]
            solutions = ["检查网络连接", "重新登录iCloud", "重启设备"]
            
        case .relinquishingLibraryBundleToWriter:
            description = "照片库正在被其他进程写入"
            causes = ["多个应用同时访问照片库", "系统正在同步照片"]
            solutions = ["等待同步完成", "重启应用"]
            
        case .switchingSystemPhotoLibrary:
            description = "正在切换系统照片库"
            causes = ["系统正在切换照片库", "iCloud设置变更"]
            solutions = ["等待切换完成", "检查iCloud照片设置"]
            
        case .networkAccessRequired:
            description = "需要网络访问"
            causes = ["照片存储在iCloud中", "网络连接不可用"]
            solutions = ["连接网络", "等待照片下载完成"]
            
        default:
            description = "照片库错误 (代码: \(errorCode))"
            causes = ["照片库访问问题", "系统资源不足", "权限问题"]
            solutions = ["重新授权照片访问", "重启应用", "检查存储空间"]
        }
        
        return PHErrorDiagnostic(
            errorCode: errorCode,
            description: description,
            possibleCauses: causes,
            solutions: solutions
        )
    }
    
    /// 生成错误报告
    func generateErrorReport(for errors: [String]) -> String {
        let diagnostic = performFullDiagnostics()
        
        var report = """
        
        🔍 MPhotos 系统错误诊断报告
        ================================
        
        📅 诊断时间: \(DateFormatter.localizedString(from: diagnostic.timestamp, dateStyle: .medium, timeStyle: .medium))
        
        ❌ 遇到的错误:
        \(errors.joined(separator: "\n"))
        
        ✅ 系统状态正常项:
        \(diagnostic.successes.joined(separator: "\n"))
        
        """
        
        if !diagnostic.issues.isEmpty {
            report += """
            
            ⚠️ 发现的问题:
            \(diagnostic.issues.joined(separator: "\n"))
            
            """
        }
        
        if !diagnostic.solutions.isEmpty {
            report += """
            
            💡 建议的解决方案:
            \(diagnostic.solutions.joined(separator: "\n"))
            
            """
        }
        
        report += """
        
        🛠️ 通用解决步骤:
        1. 重启MPhotos应用
        2. 检查照片访问权限设置
        3. 确保设备有足够存储空间
        4. 检查网络连接（如使用iCloud照片）
        5. 重启设备
        6. 如问题持续，请联系技术支持
        
        ================================
        
        """
        
        return report
    }
    
    // MARK: - 辅助方法
    
    private func getAvailableStorageSpace() -> Int64? {
        do {
            let systemAttributes = try FileManager.default.attributesOfFileSystem(forPath: NSHomeDirectory())
            return systemAttributes[.systemFreeSize] as? Int64
        } catch {
            return nil
        }
    }
    
    private func getMemoryUsage() -> (used: Double, total: Double) {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let usedMB = Double(info.resident_size) / 1024 / 1024
            return (used: usedMB, total: 0) // 总内存需要其他方法获取
        } else {
            return (used: 0, total: 0)
        }
    }
    
    private func isNetworkAvailable() -> Bool {
        // 简单的网络检查
        return true // 实际实现需要使用Reachability
    }
}

// MARK: - 数据结构

struct SystemDiagnosticResult {
    let successes: [String]
    let issues: [String]
    let solutions: [String]
    let timestamp: Date
    
    var hasIssues: Bool {
        return !issues.isEmpty
    }
}

struct PHErrorDiagnostic {
    let errorCode: Int
    let description: String
    let possibleCauses: [String]
    let solutions: [String]
}

// MARK: - 扩展方法

extension SystemErrorDiagnostics {
    
    /// 快速诊断并打印结果
    func quickDiagnose() {
        let result = performFullDiagnostics()
        
        print("🔍 MPhotos 快速诊断结果:")
        print("✅ 正常项: \(result.successes.count)")
        print("⚠️ 问题项: \(result.issues.count)")
        
        if result.hasIssues {
            print("\n发现的问题:")
            result.issues.forEach { print("  - \($0)") }
            print("\n建议的解决方案:")
            result.solutions.forEach { print("  - \($0)") }
        } else {
            print("🎉 系统状态良好！")
        }
    }
}

#endif
