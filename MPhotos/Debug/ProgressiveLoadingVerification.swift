//
//  ProgressiveLoadingVerification.swift
//  MPhotos
//
//  Created for verifying progressive loading functionality
//

#if DEBUG

import Foundation
import UIKit
import Photos

/// 渐进式加载功能验证工具
class ProgressiveLoadingVerification {

    static let shared = ProgressiveLoadingVerification()

    private init() {}
    
    // MARK: - 验证方法
    
    /// 验证渐进式加载架构是否正确实现
    func verifyProgressiveLoadingArchitecture() -> VerificationResult {
        var issues: [String] = []
        var successes: [String] = []
        
        // 1. 验证PhotoLibraryService是否正确初始化
        _ = PhotoLibraryService.shared
        successes.append("✅ PhotoLibraryService单例正确初始化")
        
        // 2. 验证ProgressiveLoadingManager是否可用
        let mockImageManager = PHCachingImageManager()
        let mockCache = NSCache<NSString, UIImage>()

        let progressiveManager = ProgressiveLoadingManager(
            imageManager: mockImageManager,
            thumbnailCache: mockCache
        )

        if progressiveManager.getActiveRequestCount() == 0 {
            successes.append("✅ ProgressiveLoadingManager正确创建")
        } else {
            issues.append("❌ ProgressiveLoadingManager初始状态异常")
        }
        
        // 3. 验证API兼容性
        let service2 = PhotoLibraryService.shared

        // 测试取消方法是否存在
        service2.cancelAllThumbnailRequests()
        successes.append("✅ 取消请求API可用")

        // 测试缓存清理方法
        service2.clearMemoryCache()
        successes.append("✅ 缓存清理API可用")

        // 测试缓存状态获取
        let status = service2.getCacheStatus()
        if !status.isEmpty {
            successes.append("✅ 缓存状态API可用")
        } else {
            issues.append("❌ 缓存状态API返回空结果")
        }
        
        // 4. 验证内存管理配置
        let service3 = PhotoLibraryService.shared
        let status2 = service3.getCacheStatus()

        if status2.contains("缓存状态") {
            successes.append("✅ 内存管理配置正常")
        } else {
            issues.append("❌ 内存管理配置异常")
        }
        
        // 5. 验证错误处理
        let errors: [PhotoLibraryError] = [
            .permissionDenied,
            .imageLoadFailed,
            .assetNotFound,
            .invalidSettings
        ]

        for error in errors {
            if error.errorDescription != nil {
                continue
            } else {
                issues.append("❌ 错误处理缺少描述: \(error)")
            }
        }

        successes.append("✅ 错误处理机制正常")
        
        return VerificationResult(successes: successes, issues: issues)
    }
    
    /// 验证性能配置
    func verifyPerformanceConfiguration() -> PerformanceVerificationResult {
        let memoryInfo = getDeviceMemoryInfo()
        let cacheStatus = PhotoLibraryService.shared.getCacheStatus()

        return PerformanceVerificationResult(
            deviceMemoryGB: memoryInfo,
            cacheConfiguration: cacheStatus,
            recommendedSettings: generateRecommendedSettings(for: memoryInfo)
        )
    }
    
    /// 生成验证报告
    func generateVerificationReport() -> String {
        let architectureResult = verifyProgressiveLoadingArchitecture()
        let performanceResult = verifyPerformanceConfiguration()
        
        let report = """
        
        🔍 渐进式加载架构验证报告
        ================================
        
        📊 架构验证结果:
        \(architectureResult.successes.joined(separator: "\n"))
        
        \(architectureResult.issues.isEmpty ? "" : "⚠️ 发现的问题:\n\(architectureResult.issues.joined(separator: "\n"))")
        
        📈 性能配置验证:
        设备内存: \(String(format: "%.1f", performanceResult.deviceMemoryGB))GB
        
        \(performanceResult.cacheConfiguration)
        
        💡 推荐设置:
        \(performanceResult.recommendedSettings)
        
        ================================
        验证时间: \(Date())
        架构状态: \(architectureResult.issues.isEmpty ? "✅ 正常" : "⚠️ 需要关注")
        
        """
        
        return report
    }
    
    // MARK: - 辅助方法
    
    private func getDeviceMemoryInfo() -> Double {
        let physicalMemory = ProcessInfo.processInfo.physicalMemory
        let memoryGB = Double(physicalMemory) / (1024 * 1024 * 1024)
        return memoryGB
    }
    
    private func generateRecommendedSettings(for memoryGB: Double) -> String {
        switch memoryGB {
        case 8...:
            return "高端设备配置: 400MB缓存, 400张图片限制"
        case 6..<8:
            return "中高端设备配置: 300MB缓存, 350张图片限制"
        case 4..<6:
            return "中端设备配置: 250MB缓存, 300张图片限制"
        default:
            return "入门设备配置: 200MB缓存, 250张图片限制"
        }
    }
}

// MARK: - 结果数据结构

struct VerificationResult {
    let successes: [String]
    let issues: [String]
    
    var isSuccessful: Bool {
        return issues.isEmpty
    }
}

struct PerformanceVerificationResult {
    let deviceMemoryGB: Double
    let cacheConfiguration: String
    let recommendedSettings: String
}

// MARK: - 调试扩展

extension ProgressiveLoadingVerification {
    
    /// 在调试模式下打印验证报告
    func printVerificationReport() {
        let report = generateVerificationReport()
        print(report)
    }
    
    /// 快速验证（用于开发时调试）
    func quickVerify() -> Bool {
        let result = verifyProgressiveLoadingArchitecture()
        
        if result.isSuccessful {
            print("🎉 渐进式加载架构验证通过")
            return true
        } else {
            print("⚠️ 渐进式加载架构验证发现问题:")
            result.issues.forEach { print("  - \($0)") }
            return false
        }
    }
}

#endif
