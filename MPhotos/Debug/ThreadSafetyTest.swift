//
//  ThreadSafetyTest.swift
//  MPhotos
//
//  Created for testing thread safety fixes
//

import Foundation
import UIKit

/// 线程安全测试工具
class ThreadSafetyTest {
    
    /// 测试文件操作的线程安全性
    static func testFileOperationSafety() {
        print("🧪 开始线程安全测试...")
        
        let testFileURL = getTestFileURL()
        let testContent = "测试内容"
        
        // 创建测试文件
        try? testContent.write(to: testFileURL, atomically: true, encoding: .utf8)
        
        // 并发测试：同时读写文件
        let group = DispatchGroup()
        
        // 启动多个读取任务
        for i in 0..<10 {
            group.enter()
            DispatchQueue.global(qos: .utility).async {
                defer { group.leave() }
                
                for _ in 0..<100 {
                    // 模拟 GestureLogger 的文件操作
                    testFileRead(url: testFileURL, taskId: i)
                    Thread.sleep(forTimeInterval: 0.001)
                }
            }
        }
        
        // 启动多个写入任务
        for i in 0..<5 {
            group.enter()
            DispatchQueue.global(qos: .background).async {
                defer { group.leave() }
                
                for j in 0..<50 {
                    // 模拟文件写入
                    testFileWrite(url: testFileURL, taskId: i, iteration: j)
                    Thread.sleep(forTimeInterval: 0.002)
                }
            }
        }
        
        group.notify(queue: .main) {
            print("✅ 线程安全测试完成")
            // 清理测试文件
            try? FileManager.default.removeItem(at: testFileURL)
        }
    }
    
    /// 测试文件读取
    private static func testFileRead(url: URL, taskId: Int) {
        do {
            // 安全的文件读取
            guard FileManager.default.fileExists(atPath: url.path) else {
                return
            }
            
            let _ = try String(contentsOf: url)
            // print("📖 任务\(taskId): 读取成功")
            
        } catch {
            print("⚠️ 任务\(taskId): 读取失败 - \(error.localizedDescription)")
        }
    }
    
    /// 测试文件写入
    private static func testFileWrite(url: URL, taskId: Int, iteration: Int) {
        do {
            let content = "任务\(taskId)-迭代\(iteration): \(Date())\n"
            
            // 原子写入
            let existingContent = (try? String(contentsOf: url)) ?? ""
            let newContent = existingContent + content
            try newContent.write(to: url, atomically: true, encoding: .utf8)
            
            // print("✏️ 任务\(taskId): 写入成功")
            
        } catch {
            print("⚠️ 任务\(taskId): 写入失败 - \(error.localizedDescription)")
        }
    }
    
    /// 获取测试文件URL
    private static func getTestFileURL() -> URL {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        return documentsPath.appendingPathComponent("thread_safety_test.txt")
    }
    
    /// 测试内存访问安全性
    static func testMemoryAccessSafety() {
        print("🧪 开始内存访问安全测试...")
        
        let group = DispatchGroup()
        
        // 并发访问内存信息
        for i in 0..<20 {
            group.enter()
            DispatchQueue.global(qos: .utility).async {
                defer { group.leave() }
                
                for _ in 0..<50 {
                    let memoryUsage = getMemoryUsageSafely()
                    if i == 0 { // 只有第一个任务打印，避免日志过多
                        print("💾 内存使用: \(memoryUsage)MB")
                    }
                    Thread.sleep(forTimeInterval: 0.01)
                }
            }
        }
        
        group.notify(queue: .main) {
            print("✅ 内存访问安全测试完成")
        }
    }
    
    /// 安全获取内存使用量
    private static func getMemoryUsageSafely() -> Int {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Int(info.resident_size) / 1024 / 1024
        } else {
            return 0
        }
    }
    
    /// 运行所有测试
    static func runAllTests() {
        print("🚀 开始所有线程安全测试...")
        
        testFileOperationSafety()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            testMemoryAccessSafety()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            print("🏁 所有线程安全测试完成")
        }
    }
}
